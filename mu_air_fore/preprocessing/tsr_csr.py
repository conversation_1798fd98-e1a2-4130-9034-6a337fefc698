# -*- coding: utf-8 -*-
"""
阶段①：计算 TSR / CSR 差分，并保存中间文件
"""
from pathlib import Path
from typing import Optional

import pandas as pd

from mu_air_fore.config import DATA_DIR

SRC_FILE = DATA_DIR / "数据.xlsx"
PREPROCESS_OUTPUT = DATA_DIR / "送修合同清单TSR_CSR更正.xlsx"


def _calc_diff(group: pd.DataFrame) -> pd.DataFrame:
    """
    对同一 (件号, 序号) 分组后，计算 TSN/CSN 差分
    """
    tsr = group["TSN"].shift(-1) - group["TSN"]
    csr = group["CSN"].shift(-1) - group["CSN"]
    tsr.iloc[-1] = None  # 最后一条置空
    csr.iloc[-1] = None
    group = group.copy()
    group["TSR"] = tsr
    group["CSR"] = csr
    return group


def preprocess_tsr_csr(src: Optional[Path] = None, dst: Optional[Path] = None) -> Path:
    """返回生成文件路径"""
    src = src or SRC_FILE
    dst = dst or PREPROCESS_OUTPUT

    df = pd.read_excel(src, sheet_name="送修合同清单")
    df["合同日期"] = pd.to_datetime(df["合同日期"], errors="coerce")
    df = df.sort_values(["件号", "序号", "合同日期"]).reset_index(drop=True)

    df = (
        df.groupby(["件号", "序号"], group_keys=False)
        .apply(_calc_diff)
    )

    dst.parent.mkdir(exist_ok=True, parents=True)
    df.to_excel(dst, index=False)
    print(f"[Preprocess] 中间文件已生成：{dst}")
    return dst