# -*- coding: utf-8 -*-
"""
集中管理常量、路径、映射表
"""
from pathlib import Path

# ---------------------- 通用路径 ---------------------- #
PROJECT_ROOT = Path(__file__).resolve().parents[2]
DATA_DIR: Path = PROJECT_ROOT / "data"

# ---------------------- 汇率 ---------------------- #
USD_TO_RMB_RATE: float = 7.0

# ---------------------- 修理厂 & 价格映射 ---------------------- #
TARGET_SUPPLIERS = [
    "0000100001 东方航空技术有限公司附件部西北附件部",
    "0000100002 东方航空技术有限公司附件部虹桥附件部",
    "0000100077 东航技术西北分公司大修部",
]

PRICE_MAP = {
    "1706903": {"测试": 0.0, "修理": 491_700.0, "翻修": 0.0, "改装": 0.0},
    "740119G": {"测试": 0.0, "修理": 259_100.0, "翻修": 0.0, "改装": 280_000.0},
    "740119H": {"测试": 0.0, "修理": 434_900.0, "翻修": 0.0, "改装": 400_000.0},
    "752168B": {"测试": 0.0, "修理": 451_500.0, "翻修": 0.0, "改装": 101_000.0},
    "752168C": {"测试": 0.0, "修理": 451_500.0, "翻修": 0.0, "改装": 0.0},
    "761574B": {"测试": 0.0, "修理": 274_050.0, "翻修": 0.0, "改装": 151_950.0},
}

LEVEL_MAP = {
    "INSP": ["检查"],
    "M&O": ["改装", "翻修"],
    "M&R": ["改装", "修理"],
    "MOD": ["改装"],
    "RP": ["修理"],
}

# ---------------------- 长表列顺序 ---------------------- #
LONG_COLS = [
    "件号", "序号", "合同类型", "供应商", "维修级别", "报价",
    "TSN", "CSN", "TSR", "CSR", "合同日期", "状态", "合同号", "是否补TSR",
    "维修次数", "组异常类型", "第几次维修", "单条记录异常类型",
]