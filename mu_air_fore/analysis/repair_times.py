# -*- coding: utf-8 -*-
"""
阶段②：维修次数分析 & 异常检测 & 报表生成
"""
from __future__ import annotations

from pathlib import Path
from typing import List, Tuple

import numpy as np
import pandas as pd

from mu_air_fore.config import (
    DATA_DIR,
    USD_TO_RMB_RATE,
    TARGET_SUPPLIERS,
    PRICE_MAP,
    LEVEL_MAP,
    LONG_COLS,
)
from mu_air_fore.io import write_excel

# ---------- 输入 / 输出路径 ----------
CORRECTED_INPUT = DATA_DIR / "送修合同清单TSR_CSR更正.xlsx"
TSN_FILE = DATA_DIR / "采购合同及TSN.xlsx"
LONG_OUTPUT = DATA_DIR / "修理次数14_长表.xlsx"
WIDE_OUTPUT = DATA_DIR / "修理次数14_宽表.xlsx"


# ------------------ 工具函数 ------------------ #
def _convert_currency(df: pd.DataFrame) -> None:
    if {"货币", "报价"}.issubset(df.columns):
        df["货币"] = df["货币"].astype(str).str.strip().str.upper()
        usd_mask = df["货币"] == "USD"
        df.loc[usd_mask, "报价"] = (
            pd.to_numeric(df.loc[usd_mask, "报价"], errors="coerce") * USD_TO_RMB_RATE
        )
        df.loc[usd_mask, "货币"] = "RMB"


def _supplier_price(row: pd.Series) -> float:
    if row["供应商"] not in TARGET_SUPPLIERS:
        return row["报价"]

    part_no = row["件号"]
    level_str = str(row["维修级别"]).strip().upper()
    if part_no not in PRICE_MAP:
        return row["报价"]

    for key, levels in LEVEL_MAP.items():
        if key in level_str:
            return sum(PRICE_MAP[part_no].get(lv, 0.0) for lv in levels)
    return row["报价"]


# ----------- 组异常判定 ----------- #
def _group_exit(sub: pd.DataFrame) -> bool:
    last = sub.iloc[-1]
    if str(last.get("状态", "")).strip().upper() == "CLOSE":
        try:
            return float(last["TSR"]) == 0
        except Exception:
            return False
    return False


def _group_first_repair_time_err(sub: pd.DataFrame) -> bool:
    try:
        first_tsn = float(sub["TSN"].iloc[0])
    except Exception:
        return False
    return first_tsn >= 30_000


# ----------- 单条异常判定 ----------- #
def _single_abnormal(row: pd.Series) -> str:
    abn: List[str] = []
    is_last = row["是否最后一次"]
    contract_date = row["合同日期"]

    # 合同未报价
    if is_last and pd.notna(contract_date) and getattr(contract_date, "year", None) == 2025:
        if pd.isna(row["报价"]) or float(row["报价"]) == 0:
            abn.append("合同未报价")

    # 未检测出故障 / 未修好
    if not (is_last and row["是否补TSR"] == "是"):
        tsr_val = float(row["TSR"]) if pd.notna(row["TSR"]) else None
        price_val = float(row["报价"]) if pd.notna(row["报价"]) else None
        if tsr_val is not None and 0 < tsr_val < 1_000:
            if price_val is not None and price_val < 10_000:
                abn.append("未检测出故障")
            elif price_val is not None and price_val >= 10_000:
                abn.append("未修好")

    # TSR 超标
    tsr_val = float(row["TSR"]) if pd.notna(row["TSR"]) else None
    if tsr_val is not None and tsr_val > 25_000:
        abn.append("TSR超标")

    # 合同执行中
    if str(row.get("状态", "")).strip().upper() == "OPEN":
        abn.append("合同执行中")

    # 合同数据异常
    if str(row.get("状态", "")).strip().upper() == "CLOSE" and not is_last:
        if tsr_val == 0:
            abn.append("合同数据异常")

    return ",".join(abn)


# ====================== 主函数 ====================== #
def analyse_repair_times(
    corrected_file: Path | None = None,
    tsn_file: Path | None = None,
) -> Tuple[Path, Path]:
    """
    返回 (长表路径, 宽表路径)
    """
    corrected_file = corrected_file or CORRECTED_INPUT
    tsn_file = tsn_file or TSN_FILE

    df = pd.read_excel(corrected_file, sheet_name="Sheet1")
    tsn_df = pd.read_excel(tsn_file, sheet_name="Sheet1")

    # 清洗格式
    for col in ["件号", "序号"]:
        df[col] = df[col].astype(str).str.strip()
        tsn_df[col] = tsn_df[col].astype(str).str.strip()

    # 过滤
    df = df[
        (df["TSR"] >= 0) & (df["CSR"] >= 0) |
        (df["TSR"].isna()) | (df["CSR"].isna())
    ].copy()

    # 汇率
    _convert_currency(df)

    # 补 TSR
    df["是否补TSR"] = "否"

    def _get_final_tsn(row: pd.Series):
        sub = tsn_df[(tsn_df["件号"] == row["件号"]) & (tsn_df["序号"] == row["序号"])]
        if sub.empty:
            return np.nan
        v = sub["截至4月TSN"].values[0]
        if pd.isna(v) or v == "":
            if "计量/计数器总TSN" in sub:
                v = sub["计量/计数器总TSN"].values[0]
        return v

    last_idx = df.groupby(["件号", "序号"]).tail(1).index
    for idx in last_idx:
        r = df.loc[idx]
        final_tsn = _get_final_tsn(r)
        if pd.notna(final_tsn):
            try:
                tsr_calc = float(final_tsn) - float(r["TSN"])
                if tsr_calc >= 0 and (
                    pd.isna(r["TSR"]) or abs(r["TSR"] - tsr_calc) > 1e-6
                ):
                    df.at[idx, "TSR"] = tsr_calc
                    df.at[idx, "是否补TSR"] = "是"
            except Exception:
                pass

    # 价格重算
    df["报价"] = df.apply(_supplier_price, axis=1)

    # 排序/次数
    df = df.sort_values(["件号", "序号", "合同日期"]).reset_index(drop=True)
    df["维修次数"] = df.groupby(["件号", "序号"])["合同日期"].transform("size")
    df["第几次维修"] = df.groupby(["件号", "序号"]).cumcount() + 1
    df["是否最后一次"] = df["第几次维修"] == df["维修次数"]

    # 单条异常
    df["单条记录异常类型"] = df.apply(_single_abnormal, axis=1)
    df = df[~df["单条记录异常类型"].str.contains("TSR超标|合同数据异常", na=False)].copy()

    # 重新计算
    df = df.sort_values(["件号", "序号", "合同日期"]).reset_index(drop=True)
    df["维修次数"] = df.groupby(["件号", "序号"])["合同日期"].transform("size")
    df["第几次维修"] = df.groupby(["件号", "序号"]).cumcount() + 1
    df["是否最后一次"] = df["第几次维修"] == df["维修次数"]

    # 组异常
    grp = df.groupby(["件号", "序号"])
    exit_map = grp.apply(_group_exit)
    first_err_map = grp.apply(_group_first_repair_time_err)

    def _group_abnormal(row: pd.Series) -> str:
        key = (row["件号"], row["序号"])
        abn: List[str] = []
        if exit_map.get(key, False):
            abn.append("件退出")
        if first_err_map.get(key, False):
            abn.append("首次修理时间错误")
        return ",".join(abn)

    df["组异常类型"] = df.apply(_group_abnormal, axis=1)

    # ------------------ 导出长表 ------------------ #
    df["维修次数"] = df["维修次数"].astype(int)
    max_cnt = int(df["维修次数"].max()) if not df.empty else 0

    sheets_long = {"完整维修履历": df[LONG_COLS]}
    for i in range(1, max_cnt + 1):
        sub = df[df["维修次数"] == i]
        if not sub.empty:
            sheets_long[f"维修次数{i}"] = sub[LONG_COLS]

    write_excel(sheets_long, LONG_OUTPUT)
    print(f"[Analysis] 长表已输出：{LONG_OUTPUT}")

    # ------------------ 导出宽表 ------------------ #
    def _make_wide_row(key, g: pd.DataFrame) -> List:
        key_tuple = (key[0], key[1])
        grp_abn = []
        if exit_map.get(key_tuple, False):
            grp_abn.append("件退出")
        if first_err_map.get(key_tuple, False):
            grp_abn.append("首次修理时间错误")
        grp_abn_str = ",".join(grp_abn)

        data = [key[0], key[1], g.shape[0], grp_abn_str]
        for _, r in g.sort_values("合同日期").iterrows():
            data.extend([
                r["合同日期"], r["TSN"], r["TSR"], r["CSN"], r["CSR"],
                r["维修级别"], r["报价"], r["供应商"], r["状态"], r["是否补TSR"],
                r["单条记录异常类型"],
            ])
        data.extend([""] * (114 - len(data)))
        return data

    rows = [_make_wide_row(k, g) for k, g in df.groupby(["件号", "序号"])]
    cols = ["件号", "序号", "维修次数", "组异常类型"]
    for i in range(1, 11):
        cols.extend([
            f"{i}-合同日期", f"{i}-TSN", f"{i}-TSR", f"{i}-CSN", f"{i}-CSR",
            f"{i}-维修级别", f"{i}-报价", f"{i}-供应商", f"{i}-状态", f"{i}-是否补TSR",
            f"{i}-单条记录异常类型",
        ])

    df_wide = pd.DataFrame(rows, columns=cols)
    df_wide["维修次数"] = pd.to_numeric(df_wide["维修次数"], errors="coerce").astype("Int64")

    sheets_wide = {"完整维修履历": df_wide}
    for i in range(1, max_cnt + 1):
        sub = df_wide[df_wide["维修次数"] == i]
        if not sub.empty:
            sheets_wide[f"维修次数{i}"] = sub

    write_excel(sheets_wide, WIDE_OUTPUT)
    print(f"[Analysis] 宽表已输出：{WIDE_OUTPUT}")

    return LONG_OUTPUT, WIDE_OUTPUT