# -*- coding: utf-8 -*-
"""
针对 Excel 的读取 / 写入封装
"""
from pathlib import Path
from typing import Union, Dict, Any, Optional

import pandas as pd
from pandas import ExcelWriter


def read_excel(path: Union[str, Path], **kwargs) -> pd.DataFrame:
    """简单封装 pd.read_excel，保证 Path 类型兼容"""
    path = Path(path)
    if not path.exists():
        raise FileNotFoundError(f"文件不存在：{path}")
    return pd.read_excel(path, **kwargs)


def write_excel(
    df_dict: Dict[str, pd.DataFrame],
    path: Union[str, Path],
    writer_kwargs: Optional[Dict[str, Any]] = None,
) -> None:
    """
    将多个 DataFrame 写入同一工作簿
    df_dict: {sheet_name: dataframe}
    """
    path = Path(path)
    writer_kwargs = writer_kwargs or {}
    with ExcelWriter(path, engine="openpyxl", **writer_kwargs) as writer:
        for sheet, df in df_dict.items():
            df.to_excel(writer, sheet_name=sheet, index=False)