# -*- coding: utf-8 -*-
"""
命令行入口：预处理 + 分析
"""
import argparse

from mu_air_fore.preprocessing import preprocess_tsr_csr
from mu_air_fore.analysis import analyse_repair_times
from mu_air_fore.db import load_repair_contracts_to_mysql, load_repair_history_to_mysql,load_borrow_in_to_mysql,load_idg_history_to_mysql,load_flight_foc_to_mysql,load_location_category_to_mysql,load_equipment_inventory_to_mysql,load_equipment_fleet_to_mysql,load_tsr_prediction_to_mysql,load_base_missing_parts,load_part_risk_summary,load_bad_part_detail,load_inventory_location
from mu_air_fore.idg import run_idg_flow, supplement_idg_parts
from mu_air_fore.idg.merge_sheets import merge_six_to_three
from mu_air_fore.idg.engine_history import generate_engine_history
from mu_air_fore.idg.patch_idg_to_3sheet import patch_idg_to_3sheet
from mu_air_fore.idg.idg_history import generate_idg_history
from mu_air_fore.idg.outbound_records_pre import preprocess_outbound
from mu_air_fore.idg.link_outbound import link_idg_outbound



def main() -> None:
    parser = argparse.ArgumentParser(
        prog="mu-air-fore",
        description="航空器件送修数据分析工具",
    )
    parser.add_argument("--skip-preprocess", action="store_true",help="跳过 TSR/CSR 预处理阶段")
    parser.add_argument("--to-db", action="store_true",help="将 Excel 结果导入 MySQL")
    parser.add_argument("--idg-flow", action="store_true", help="执行 IDG 拆换处理流程")
    args = parser.parse_args()

    if not args.skip_preprocess:
        preprocess_tsr_csr()
        analyse_repair_times()


    if args.idg_flow:
        run_idg_flow()
        merge_six_to_three()
        generate_engine_history()
        supplement_idg_parts()
        patch_idg_to_3sheet()
        generate_idg_history()
        preprocess_outbound()
        link_idg_outbound()


    if args.to_db:
        pass
    #     load_repair_contracts_to_mysql()
    #     load_repair_history_to_mysql()
    #     load_borrow_in_to_mysql()
    #     load_idg_history_to_mysql()
    #     load_flight_foc_to_mysql()
    #     load_location_category_to_mysql()
    #     load_equipment_inventory_to_mysql()
    #     load_equipment_fleet_to_mysql()
    #     load_tsr_prediction_to_mysql()
    #     load_base_missing_parts()
    #     load_part_risk_summary()
    #     load_bad_part_detail()
    #     load_inventory_location()


    print("=== 全部流程完成 ===")


if __name__ == "__main__":
    main()