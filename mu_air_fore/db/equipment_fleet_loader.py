# mu_air_fore/db/equipment_fleet_loader.py
# -*- coding: utf-8 -*-
"""
将《数据.xlsx》中的 “机队数据” 工作表导入 MySQL 表 equipment_fleet
-----------------------------------------------------------------
Excel → MySQL 字段映射见 _COL_MAP
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel ⇨ MySQL 字段映射
# --------------------------------------------------------------------
_EXCEL_FILE = DATA_DIR / "数据.xlsx"      # ← 根据实际文件名调整
_SHEET_NAME = "机队数据"                 # ← 对应工作表名称
_TABLE_NAME = "equipment_fleet"

_COL_MAP: Dict[str, str] = {
    # 基础信息
    "机号": "aircraft_no",
    "营运人": "operator",
    "文档": "document",
    "同步": "sync_flag",
    "飞机状态": "aircraft_status",
    "引进方式": "introduction_method",
    "合同批次": "contract_batch",
    "合同计划交付日期": "contract_plan_delivery_date",
    "租用日期退租日期": "lease_start_end_date",
    "退租日期": "lease_end_date",
    "是否续租": "is_renewed",
    "续租时间": "renewed_time",
    "租赁公司": "leasing_company",
    "是否NOVATION": "is_novation",
    "NOVATION公司": "novation_company",
    "飞机拥有人": "aircraft_owner",
    "CEC编号": "cec_no",
    "RC前缘力臂": "rc_leading_edge_arm",
    "MODEL": "model",
    "%RC": "rc_percent",

    # CMP方案编号有重复，两列在 Excel 中会自动变为 .1 后缀
    "CMP方案编号": "cmp_plan_no",
    "CMP方案编号.1": "cmp_plan_no_2",

    "手册代码": "manual_code",
    "生产线号": "production_line_no",
    "IPC有效号": "ipc_valid_no",
    "RNAV 工程": "rnav_engineering",
    "RVSM工程": "rvsm_engineering",
    "用户有效码": "user_valid_code",
    "宽体机标识": "widebody_flag",
    "RNP1": "rnp1",
    "ETOPS时间": "etops_time",
    "是否有翼尖小翼": "has_winglet",
    "RNP5": "rnp5",
    "RNP APCH": "rnp_apch",
    "ILS着陆能力": "ils_landing_capability",
    "最大起飞和着落海拔(M)": "max_takeoff_landing_altitude_m",
    "RNP4": "rnp4",
    "II类运行": "cat_ii_operation",
    "最大起飞和着落海拔(FT)": "max_takeoff_landing_altitude_ft",
    "RNP10": "rnp10",
    "高原": "plateau",
    "最大滑行重量(KG)": "max_taxi_weight",
    "卫星导航能力": "sat_nav_capability",
    "高高原 工程": "high_plateau_engineering",
    "最大起飞重量(KG)": "max_takeoff_weight",
    "RNP AR 工程": "rnp_ar_engineering",
    "HUD": "hud",
    "最大着落重量(KG)": "max_landing_weight",
    "东航飞机": "china_eastern_aircraft",
    "右座手轮": "right_seat_control_wheel",
    "最大无油重量(KG)": "max_zero_fuel_weight",
    "极地 工程": "polar_engineering",
    "是否具备卫星电话": "has_sat_phone",
    "飞行指引杆": "flight_director_bar",
    "WiFi": "wifi",
    "无障碍设施": "accessibility_facilities",
    "构型分组": "configuration_group",
    "ADS-B": "ads_b",
    "MMR": "mmr",
    "氧气瓶配备等级": "oxygen_bottle_level",
    "挂篮": "basket",
    "头等舱挂篮数量": "first_class_basket_count",
    "经济舱挂篮数量": "economy_class_basket_count",
    "公务舱挂篮数量": "business_class_basket_count",
    "HEPA": "hepa",
    "飞机尾流类型": "aircraft_wake_type",
    "气象雷达型号": "weather_radar_model",
    "AP/FD TCAS": "ap_fd_tcas",
    "刹车类型": "brake_type",
    "GA SOFT": "ga_soft",
    "地形数据库": "terrain_database",
    "ROW ROPS": "row_rops",
    "DSG(ESG)FC": "dsg_esg_fc",
    "LOV FC": "lov_fc",
    "DSG(ESG)预警值FC": "dsg_esg_threshold_fc",
    "LOV预警值FC": "lov_threshold_fc",
    "DSG(ESG)FH": "dsg_esg_fh",
    "LOV FH": "lov_fh",
    "DSG(ESG)预警值FH": "dsg_esg_threshold_fh",
    "LOV预警值FH": "lov_threshold_fh",
    "ADSB 工程": "adsb_engineering",
    "CMP发动机型号": "cmp_engine_model",
    "装机发动机型号": "installed_engine_model",
    "装机发动机数量": "installed_engine_quantity",
    "发动机构型": "engine_configuration",
    "装机APU件号": "installed_apu_part_no",
    "CMP APU型号": "cmp_apu_model",
    "原装机发动机序号": "original_engine_serial",
    "APU制造商": "apu_manufacturer",
    "Rating": "rating",
    "Config": "config",
    "运行-航空器地址编码（8进制）": "run_aircraft_addr_oct",
    "运行-选呼号码": "run_selcal",
    "运行-ETOPS时间": "run_etops_time",
    "运行-RVSM": "run_rvsm",
    "运行-极地": "run_polar",
    "运行-RNP AR": "run_rnp_ar",
    "运行-高高原": "run_high_plateau",
    "运行-RNAV-2": "run_rnav2",
    "运行-II类运行": "run_cat_ii",
    "运行-III类运行": "run_cat_iii",
    "运行-RNAV-10（RNP-10）": "run_rnav10",
    "运行-RNAV-1": "run_rnav1",
    "运行-RNP4": "run_rnp4",
    "运行-RNAV-5": "run_rnav5",
    "运行-ADSB": "run_adsb",
    "运行-basic RNP-1": "run_basic_rnp1",
    "运行-HUD": "run_hud",
    "运行-RNPAPCH": "run_rnp_apch",
    "运行-CPDLC": "run_cpdcl",
    "运行-RNP-2": "run_rnp2",

    # 生产 & 时间
    "机型": "aircraft_type",
    "制造序列号(MSN)": "msn",
    "客货类型（客/货）": "pax_cargo_type",
    "排班机型": "schedule_aircraft_type",
    "飞机首飞日期": "first_flight_date",
    "出厂日期": "factory_date",
    "交付使用日期": "delivery_date",
    "制造商": "manufacturer",
    "到场日期": "arrival_date",
    "初始TSN": "initial_tsn",
    "服役日期": "service_entry_date",
    "初始CSN": "initial_csn",
    "飞机全长(M)": "aircraft_length_m",
    "交付地点": "delivery_location",
    "翼展(M)": "wingspan_m",
    "TSN预估值": "tsn_estimated",
    "飞机全高(M)": "aircraft_height_m",
    "CSN预估值": "csn_estimated",

    # 座位 & 其他
    "座位数": "seat_num",
    "是否老龄": "is_aging",
    "头等舱座位数": "first_class_seats",
    "救生船": "liferaft",
    "公务舱座位数": "business_class_seats",
    "是否彩喷": "is_painted_special",
    "超经舱座位数": "premium_economy_seats",
    "旅客氧气瓶": "passenger_oxygen_bottle",
    "经济舱座位数": "economy_class_seats",
    "FMS导航数据库": "fms_nav_database",
    "备注": "remarks",
    "彩喷类型": "painting_type",

    # 优选飞机有重复
    "优选飞机": "preferred_aircraft",
    "优选飞机.1": "preferred_aircraft_2",

    "彩喷日期": "painting_date",
    "名称": "name",
    "股份执管单位": "equity_admin_unit",
    "股份执管单位简称": "equity_admin_unit_short",
    "股份执管单位变更原因": "equity_admin_unit_change_reason",
    "指定机号单位": "assigned_aircraft_unit",
}


def load_equipment_fleet_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取 Excel ➜ 写入 equipment_fleet
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ------------ 1. 读取指定 Sheet ------------
    # keep_default_na=False 避免空单元格被解析为 NaN，保持字符串空值
    df = pd.read_excel(
        excel_path,
        sheet_name=_SHEET_NAME,
        dtype=str,
        keep_default_na=False
    )

    # ------------ 2. 列重命名 & 过滤 ------------
    df = df.rename(columns=_COL_MAP)
    keep_cols: List[str] = [col for col in _COL_MAP.values() if col in df.columns]
    df = df[keep_cols]

    # ------------ 3. 去重（按机号+制造序列号(MSN)）------------
    if {"aircraft_no", "msn"}.issubset(df.columns):
        df = df.drop_duplicates(subset=["aircraft_no", "msn"])
    else:
        df = df.drop_duplicates()

    # ------------ 4. 写入数据库 ------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",   # 若需覆盖请改为 'replace'
        index=False,
        chunksize=500,
        method="multi",
    )

    # ------------ 5. 反馈 ------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] equipment_fleet 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    load_equipment_fleet_to_mysql()