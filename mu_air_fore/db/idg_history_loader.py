# -*- coding: utf-8 -*-
"""
把《IDG履历_关联出库.xlsx》导入 MySQL 表 idg_history
------------------------------------------------
Excel → MySQL 字段映射见 _COL_MAP
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel ⇨ MySQL 字段映射
# --------------------------------------------------------------------

_EXCEL_FILE = DATA_DIR / "IDG履历_关联出库.xlsx"
_TABLE_NAME = "idg_history"

_COL_MAP: Dict[str, str] = {
    "机号": "aircraft_no",
    "时间": "record_time",
    "idg1件号": "idg1_pnr",
    "idg1": "idg1_seq",
    "idg2件号": "idg2_pnr",
    "idg2": "idg2_seq",
    "原因": "reason",
    "idg1变化标记": "idg1_change_flag",
    "idg2变化标记": "idg2_change_flag",
    "idg1过账日期": "idg1_post_datetime",
    "idg1物料凭证": "idg1_material_doc",
    "idg1领料用途": "idg1_issue_purpose",
    "idg2过账日期": "idg2_post_datetime",
    "idg2物料凭证": "idg2_material_doc",
    "idg2领料用途": "idg2_issue_purpose",
}




def load_idg_history_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取 Excel ➜ 写入 idg_history
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ------------ 1. 读取首个 Sheet ------------
    df = pd.read_excel(excel_path, sheet_name=0, dtype=str)

    # ------------ 2. 列重命名 & 过滤 ------------
    df = df.rename(columns=_COL_MAP)
    keep_cols: List[str] = list(set(_COL_MAP.values()) & set(df.columns))
    df = df[keep_cols]

    # ------------ 3. 日期 / 时间列统一为 datetime64[ns] ------------
    datetime_cols = [
        "record_time",
        "idg1_post_datetime",
        "idg2_post_datetime",
    ]
    for col in datetime_cols:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors="coerce")

    # ------------ 4. 写入数据库 ------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",
        index=False,
        chunksize=2000,
        method="multi",
    )

    # ------------ 5. 反馈 ------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] idg_history 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    load_idg_history_to_mysql()