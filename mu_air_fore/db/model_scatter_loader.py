# -*- coding: utf-8 -*-
"""
把模型输出的《跨件号_综合汇总分析.xlsx》前3个sheet的数据导入 MySQL 表 model_scatter_data
---------------------------------------------------------------------------
根据件号和模型版本号从 model/modeloutput 目录中查找对应的Excel文件并加载数据
"""
from __future__ import annotations

import sys
import os
from pathlib import Path
from typing import Dict, List, Optional
import argparse

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL

# --------------------------------------------------------------------
# 配置参数
# --------------------------------------------------------------------

_TABLE_NAME = "model_scatter_data"

# 获取项目根目录（mu-air-fore目录）
_PROJECT_ROOT = Path(__file__).resolve().parents[2]  # 从 mu_air_fore/db/ 向上两级到项目根目录
_MODEL_OUTPUT_DIR = _PROJECT_ROOT / "model" / "modeloutput"

# Excel列名到数据库字段的映射
_COL_MAP: Dict[str, str] = {
    "件号": "pnr",
    "模型": "model", 
    "样本编号": "sample_id",
    "数据集": "dataset_type",
    "实际值": "actual_value",
    "预测值": "predicted_value", 
    "残差": "residual",
    "绝对残差": "absolute_residual",
    "相对误差(%)": "relative_error_percent",
    "平方误差": "squared_error"
}


def find_excel_file(part_number: str, model_version: str) -> Optional[Path]:
    """
    根据件号和模型版本查找对应的Excel文件
    
    Args:
        part_number: 件号
        model_version: 模型版本
        
    Returns:
        Excel文件路径，如果找不到返回None
    """
    # 构建目录名模式：件号_模型版本_comprehensive_analysis_时间戳
    pattern_prefix = f"{part_number}_{model_version}_件号级模型分析_"
    
    if not _MODEL_OUTPUT_DIR.exists():
        print(f"错误：模型输出目录不存在：{_MODEL_OUTPUT_DIR}")
        print(f"请确保已运行模型分析并生成了输出文件")
        print(f"当前工作目录：{Path.cwd()}")
        print(f"项目根目录：{_PROJECT_ROOT}")
        return None
    
    # 查找匹配的目录
    matching_dirs = []
    for item in _MODEL_OUTPUT_DIR.iterdir():
        if item.is_dir() and item.name.startswith(pattern_prefix):
            matching_dirs.append(item)
    
    if not matching_dirs:
        print(f"错误：未找到匹配的目录，模式：{pattern_prefix}*")
        return None
    
    # 如果有多个匹配，选择最新的（按目录名排序，时间戳在最后）
    latest_dir = sorted(matching_dirs, key=lambda x: x.name)[-1]
    print(f"找到匹配目录：{latest_dir}")
    
    # 查找Excel文件
    excel_file = latest_dir / "跨件号_综合汇总分析.xlsx"
    if not excel_file.exists():
        print(f"错误：Excel文件不存在：{excel_file}")
        return None
    
    return excel_file


def load_scatter_data_to_mysql(part_number: str, model_version: str) -> bool:
    """
    读取Excel文件的前3个sheet并写入数据库

    Args:
        part_number: 件号
        model_version: 模型版本

    Returns:
        bool: 成功返回True，失败返回False
    """
    # 查找Excel文件
    excel_path = find_excel_file(part_number, model_version)
    if not excel_path:
        return False
    
    print(f"开始加载文件：{excel_path}")
    
    try:
        # 读取Excel文件的所有sheet名称
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        print(f"发现sheet页：{sheet_names}")
        
        # 取前3个sheet
        target_sheets = sheet_names[:3]
        print(f"将处理前3个sheet：{target_sheets}")
        
        all_data = []
        
        # 逐个处理sheet
        for sheet_name in target_sheets:
            print(f"正在处理sheet：{sheet_name}")
            
            # 读取sheet数据
            df = pd.read_excel(excel_path, sheet_name=sheet_name, dtype=str)
            print(f"  原始数据行数：{len(df)}")
            
            if df.empty:
                print(f"  跳过空sheet：{sheet_name}")
                continue
            
            # 列重命名
            df = df.rename(columns=_COL_MAP)
            
            # 过滤需要的列
            keep_cols = list(set(_COL_MAP.values()) & set(df.columns))
            if not keep_cols:
                print(f"  跳过sheet {sheet_name}：未找到匹配的列")
                continue
                
            df = df[keep_cols]
            
            # 添加模型版本列
            df['model_version'] = model_version
            
            # 数据类型转换
            numeric_cols = ['actual_value', 'predicted_value', 'residual', 
                          'absolute_residual', 'relative_error_percent', 'squared_error']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 删除包含NaN的行
            df = df.dropna()
            print(f"  清洗后数据行数：{len(df)}")
            
            if not df.empty:
                all_data.append(df)
        
        if not all_data:
            print("警告：没有有效数据可以导入")
            return False
        
        # 合并所有数据
        final_df = pd.concat(all_data, ignore_index=True)
        print(f"合并后总数据行数：{len(final_df)}")
        
        # 写入数据库
        print("开始写入数据库...")
        eng = create_engine(SQLALCHEMY_URL, echo=False)
        final_df.to_sql(
            name=_TABLE_NAME,
            con=eng,
            if_exists="append",
            index=False,
            chunksize=2000,
            method="multi",
        )
        
        # 反馈结果
        with eng.begin() as conn:
            total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
        
        print(f"[DB] {_TABLE_NAME} 导入完成，新增 {len(final_df)} 行；当前表总记录 {total} 行.")
        return True

    except Exception as e:
        print(f"错误：处理文件时发生异常：{e}")
        return False


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='加载模型散点数据到MySQL')
    parser.add_argument('part_number', help='件号')
    parser.add_argument('model_version', help='模型版本')
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    print(f"=== 模型散点数据加载器 ===")
    print(f"件号: {args.part_number}")
    print(f"模型版本: {args.model_version}")
    print(f"目标表: {_TABLE_NAME}")
    print("=" * 40)
    
    success = load_scatter_data_to_mysql(args.part_number, args.model_version)
    if success:
        print("✅ 数据加载完成")
    else:
        print("❌ 数据加载失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
