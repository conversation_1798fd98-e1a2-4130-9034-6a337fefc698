#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型散点数据加载示例脚本
演示如何使用 model_scatter_loader.py 加载不同件号和版本的数据
"""

import subprocess
import sys
from pathlib import Path

def run_loader(part_number: str, model_version: str):
    """运行数据加载器"""
    print(f"\n{'='*60}")
    print(f"加载数据：件号={part_number}, 版本={model_version}")
    print(f"{'='*60}")
    
    try:
        # 构建命令
        script_path = Path(__file__).parent / "model_scatter_loader.py"
        cmd = [sys.executable, str(script_path), part_number, model_version]
        
        # 运行命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # 显示输出
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
            
        print("✅ 加载成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 加载失败，错误代码: {e.returncode}")
        if e.stdout:
            print("输出:", e.stdout)
        if e.stderr:
            print("错误:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def batch_load_example():
    """批量加载示例"""
    print("=== 批量加载模型散点数据示例 ===")
    
    # 定义要加载的数据
    load_tasks = [
        ("1706903", "v1.0"),
        ("1706903", "v2.0"),
        ("740119G", "v1.0"),
        # 可以添加更多任务
    ]
    
    results = []
    for part_number, model_version in load_tasks:
        success = run_loader(part_number, model_version)
        results.append((part_number, model_version, success))
    
    # 显示汇总结果
    print(f"\n{'='*60}")
    print("批量加载结果汇总:")
    print(f"{'='*60}")
    
    success_count = 0
    for part_number, model_version, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} | {part_number} | {model_version}")
        if success:
            success_count += 1
    
    print(f"\n总计: {len(results)} 个任务，成功 {success_count} 个，失败 {len(results) - success_count} 个")

def interactive_load():
    """交互式加载"""
    print("=== 交互式模型数据加载 ===")
    
    while True:
        print("\n请输入要加载的数据信息:")
        part_number = input("件号: ").strip()
        if not part_number:
            print("件号不能为空")
            continue
            
        model_version = input("模型版本: ").strip()
        if not model_version:
            print("模型版本不能为空")
            continue
        
        # 确认加载
        confirm = input(f"确认加载 {part_number} - {model_version} 的数据? (y/n): ").strip().lower()
        if confirm != 'y':
            print("取消加载")
            continue
        
        # 执行加载
        success = run_loader(part_number, model_version)
        
        # 询问是否继续
        continue_load = input("\n是否继续加载其他数据? (y/n): ").strip().lower()
        if continue_load != 'y':
            break
    
    print("交互式加载结束")

def main():
    """主函数"""
    print("模型散点数据加载工具")
    print("请选择操作模式:")
    print("1. 批量加载示例")
    print("2. 交互式加载")
    print("3. 单次加载")
    print("4. 退出")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        batch_load_example()
    elif choice == "2":
        interactive_load()
    elif choice == "3":
        part_number = input("请输入件号: ").strip()
        model_version = input("请输入模型版本: ").strip()
        if part_number and model_version:
            run_loader(part_number, model_version)
        else:
            print("件号和模型版本不能为空")
    elif choice == "4":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
