# mu_air_fore/db/equipment_inventory_loader.py
# -*- coding: utf-8 -*-
"""
将《数据.xlsx》中的 “设备清单及库存” 工作表导入 MySQL 表 equipment_inventory
-----------------------------------------------------------------
Excel → MySQL 字段映射见 _COL_MAP
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel ⇨ MySQL 字段映射
# --------------------------------------------------------------------
_EXCEL_FILE = DATA_DIR / "数据.xlsx"           # 根据实际文件路径调整
_SHEET_NAME = "设备清单及库存"                 # 对应工作表名称
_TABLE_NAME = "equipment_inventory"

_COL_MAP: Dict[str, str] = {
    "设备": "equipment_no",
    "技术对象说明": "tech_obj_desc",
    "物料": "material",
    "序列号": "serial_no",
    "维护工厂": "maintenance_plant",
    "库存地点": "inventory_location",
    "特殊库存": "special_stock",
    "设备种类": "equipment_type",
    "系统状态": "system_status",
    "库存批次": "inventory_batch",
    "位置": "position_info",
    "高级设备": "advanced_equipment",
    "供应商": "vendor",
}


def load_equipment_inventory_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取 Excel ➜ 写入 equipment_inventory
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ------------ 1. 读取指定 Sheet ------------
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)

    # ------------ 2. 列重命名 & 过滤 ------------
    df = df.rename(columns=_COL_MAP)
    keep_cols: List[str] = [col for col in _COL_MAP.values() if col in df.columns]
    df = df[keep_cols]

    # ------------ 3. 去重（可根据业务选择合适字段）------------
    df = df.drop_duplicates(subset=["equipment_no", "serial_no"])

    # ------------ 4. 写入数据库 ------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",     # 改为 'replace' 可清空重载
        index=False,
        chunksize=1000,
        method="multi",
    )

    # ------------ 5. 反馈 ------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] equipment_inventory 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    load_equipment_inventory_to_mysql()