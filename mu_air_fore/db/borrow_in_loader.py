# -*- coding: utf-8 -*-
"""
把 “数据.xlsx” 的『借入清单』Sheet 写入 MySQL 表 borrow_in_list
-----------------------------------------------------------------
Excel → MySQL 字段映射见 _COL_MAP
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel ⇨ MySQL 字段映射（已对齐表字段）
# --------------------------------------------------------------------

_EXCEL_FILE = DATA_DIR / "数据.xlsx"
_SHEET_NAME = "借入清单"
_TABLE_NAME = "borrow_in_list"

_COL_MAP: Dict[str, str] = {
    "状态": "status",
    "合同创建人": "contract_creator",
    "采购凭证": "purchase_doc",
    "购买/租赁": "purchase_or_rent",
    "借入日期": "borrow_datetime",
    "入库日期": "storage_datetime",
    "MM机型": "mm_model",
    "机号": "aircraft_no",
    "短文本": "short_text",
    "物料": "material",
    "序列号": "serial_no",
    "采购订单数量": "po_qty",
    "维管网任务编号": "mw_task_no",
    "供应商": "supplier",
    "创建日期": "create_datetime",
    "备注": "remark",
    "合同库存地": "contract_stock_loc",
    "合同主控单位": "contract_main_unit",
    "参考价": "reference_price",
    "货币": "currency",
    "归还过账日期": "return_posting_datetime"
}


def load_borrow_in_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取 Excel『借入清单』Sheet ➜ 写入 borrow_in_list
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ------------ 1. 读取 ------------
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)

    # ------------ 2. 列重命名 & 过滤 ------------
    df = df.rename(columns=_COL_MAP)
    keep_cols: List[str] = [v for v in _COL_MAP.values() if v in df.columns]
    df = df[keep_cols]

    # 3. 日期列统一转 datetime64[ns]
    date_cols = [
        "borrow_datetime",
        "storage_datetime",
        "create_datetime",
        "return_posting_datetime",
    ]
    for col in date_cols:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors="coerce")

    # 4. 数值列：参考价 / 采购订单数量
    if "reference_price" in df.columns:
        df["reference_price"] = pd.to_numeric(df["reference_price"], errors="coerce")
    if "po_qty" in df.columns:
        df["po_qty"] = pd.to_numeric(df["po_qty"], errors="coerce").astype("Int64")

    # ------------ 5. 写入数据库 ------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",
        index=False,
        chunksize=2000,
        method="multi",
    )

    # ------------ 6. 反馈 ------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] borrow_in_list 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    load_borrow_in_to_mysql()