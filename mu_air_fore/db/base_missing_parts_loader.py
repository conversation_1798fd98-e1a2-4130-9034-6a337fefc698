# -*- coding: utf-8 -*-
"""
《航材采购与库存风险分析报告.xlsx》→ 各基地缺件详情
Sheet : 各基地缺件详情  →  MySQL 表 base_missing_parts_detail
"""

from __future__ import annotations
from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

_EXCEL_FILE: Path = DATA_DIR / "航材采购与库存风险分析报告.xlsx"
_SHEET_NAME = "各基地缺件详情"
_TABLE_NAME = "base_missing_parts_detail"

_COL_MAP: Dict[str, str] = {
    "基地":   "base",
    "件号":   "pnr",
    "缺件数量": "missing_qty",
    "涉及机号": "aircraft_nos",
    "涉及序号": "seqs",
}


def load_base_missing_parts(excel_path: Path | None = None) -> None:
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(excel_path)

    # 1. 读取
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)

    # 2. 列映射
    df.columns = df.columns.str.strip()
    lower_map = {k.lower(): v for k, v in _COL_MAP.items()}
    df = df.rename(columns=lambda c: lower_map.get(c.lower(), c))
    df = df[[v for v in _COL_MAP.values() if v in df.columns]]

    if df.empty:
        print("[Warn] 读取结果为空，已退出。")
        return

    # 3. 写库
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(_TABLE_NAME, eng, if_exists="append", index=False,
              chunksize=2000, method="multi")

    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] base_missing_parts_detail 导入完成，新增 {len(df)} 行，总记录 {total} 行.")


if __name__ == "__main__":
    load_base_missing_parts()