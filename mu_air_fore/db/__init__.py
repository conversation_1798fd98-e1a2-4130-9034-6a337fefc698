# -*- coding: utf-8 -*-
"""
数据库工具
"""
from .repair_contracts_loader import load_repair_contracts_to_mysql
from .borrow_in_loader import load_borrow_in_to_mysql
from .repair_history_loader import load_repair_history_to_mysql
from .idg_history_loader import load_idg_history_to_mysql
from .flight_foc_loader import load_flight_foc_to_mysql
from .location_category_loader import load_location_category_to_mysql
from .equipment_inventory_loader import load_equipment_inventory_to_mysql
from .equipment_fleet_loader import load_equipment_fleet_to_mysql
from .tsr_prediction_loader import load_tsr_prediction_to_mysql
from .base_missing_parts_loader import load_base_missing_parts
from .part_risk_summary_loader import load_part_risk_summary
from .bad_part_detail_loader import load_bad_part_detail
from .inventory_location_loader import load_inventory_location

__all__ = [
    "load_repair_contracts_to_mysql",
    "load_repair_history_to_mysql",
    "load_borrow_in_to_mysql",
    "load_idg_history_to_mysql",
    "load_flight_foc_to_mysql",
    "load_location_category_to_mysql",
    "load_equipment_inventory_to_mysql",
    "load_equipment_fleet_to_mysql",
    "load_tsr_prediction_to_mysql",
    "load_base_missing_parts",
    "load_part_risk_summary",
    "load_bad_part_detail",
    "load_inventory_location",
]