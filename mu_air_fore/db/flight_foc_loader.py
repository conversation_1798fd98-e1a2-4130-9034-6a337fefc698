# -*- coding: utf-8 -*-
"""
把 “数据.xlsx” 中 “航班202505-06” Sheet 页导入 flight_foc 表
若映射列在数据库不存在，会自动忽略并打印 WARN，避免 Unknown column 报错。
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text, inspect

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel -> MySQL 列映射
#   键:  Excel 表头（中文）
#   值:  flight_foc 表字段（与建表语句保持一致）
# --------------------------------------------------------------------
_COL_MAP: Dict[str, str] = {
    "航班ID": "flight_id",
    "航班联线号": "flight_line_no",
    "联线序号": "line_seq_no",
    "航班号": "flight_no",
    "机号": "aircraft_no",
    "飞行日期": "flight_date",

    "起飞航站": "dep_airport",
    "到达航站": "arr_airport",
    "实际出发站点": "actual_dep_site",
    "实际到达站点": "actual_arr_site",

    "VIP等级": "vip_level",
    "需担架": "need_stretcher",
    "取消担架原因": "cancel_stretcher_reason",
    "用氧": "use_oxygen",
    "取消用氧原因": "cancel_oxygen_reason",

    "计划起飞日期": "plan_dep_date",
    "计划起飞时间": "plan_dep_time",
    "预飞日期": "preflight_date",
    "预飞时间": "preflight_time",

    "计划到达日期": "plan_arr_date",
    "计划到达时间": "plan_arr_time",
    "预到日期": "pre_arr_date",
    "预到时间": "pre_arr_time",

    "执飞基地": "operate_base",
    "维护工厂": "maintenance_factory",
    "排班机型": "schedule_aircraft_type",
    "营销委机型": "marketing_aircraft_type",

    "航线变异标识": "route_variation_flag",
    "航节序号": "segment_seq",
    "后续航班": "next_flight",
    "航班性质": "flight_nature",
    "飞机中转停留时间": "aircraft_stop_time",

    "文件名　": "file_name",          # Excel 原列名末尾为全角空格
    "SSIMID(2-26)": "ssim_id",
    "空中时间": "air_time",
    "正常起落次数": "normal_tl_cnt",

    "发布人->AOC": "publisher_aoc",
    "发布日期->AOC": "publish_date_aoc",
    "发布时间->AOC": "publish_time_aoc",
    "状态": "status",

    "实际起飞日期": "actual_dep_date",
    "实际起飞时间": "actual_dep_time",
    "实际到达日期": "actual_arr_date",
    "实际到达时间": "actual_arr_time",

    "关车日期": "off_block_date",
    "关车时间": "off_block_time",
    "开车日期": "on_block_date",
    "开车时间": "on_block_time",

    "起飞日期": "takeoff_date",
    "起飞时间": "takeoff_time",
    "落地日期": "landing_date",
    "落地时间": "landing_time",

    "AOC编码": "aoc_code",
    "固定航班": "fixed_flight",
    "前续航班ID": "prev_flight_id",
    "后续航班ID": "next_flight_id",

    "航班信息变更标志": "flight_info_chg_flag",
    "变更标识": "change_flag",
    "航班起飞状态": "takeoff_status",
    "航班降落状态": "landing_status",

    "起飞航站楼": "dep_terminal",
    "到达航站楼": "arr_terminal",
    "上传AOC成功标识": "upload_aoc_flag",

    "客装货": "passenger_cargo",
    "取消客装货原因": "cancel_pc_reason",

    "创建人": "creator",
    "建立日期": "create_date",
    "创建时间": "create_time",
    "更改人": "modifier",
    "更改日期": "modify_date",
    "更改时间": "modify_time",

    "UTC航班日期": "utc_flight_date",
}

_EXCEL_FILE: Path = DATA_DIR / "数据.xlsx"
_SHEET_NAME: str = "航班202505-06"
_TABLE_NAME: str = "flight_foc"

# 需要保持 TRUE/FALSE/空 的布尔列
_BOOL_FIELDS: List[str] = [
    "need_stretcher",
    "use_oxygen",
    "fixed_flight",
    "upload_aoc_flag",
]

# --------------------------------------------------------------------
# 工具函数
# --------------------------------------------------------------------
def _clean_bool_fields(df: pd.DataFrame) -> pd.DataFrame:
    """
    将布尔列统一转大写字符串，保持原值，不做 0/1 映射
    """
    for col in _BOOL_FIELDS:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip().str.upper()
    return df


def _filter_columns_to_table(df: pd.DataFrame, eng) -> pd.DataFrame:
    """
    仅保留 flight_foc 表存在的列；其余丢弃并打印 WARN
    """
    insp = inspect(eng)
    if not insp.has_table(_TABLE_NAME):
        raise ValueError(f"数据库中不存在表 `{_TABLE_NAME}`")

    table_cols = {col["name"] for col in insp.get_columns(_TABLE_NAME)}
    df_cols = set(df.columns)

    valid_cols = [c for c in df.columns if c in table_cols]
    dropped = df_cols - table_cols
    if dropped:
        print(f"[WARN] 以下列在表 `{_TABLE_NAME}` 中不存在，已忽略：{', '.join(sorted(dropped))}")

    return df[valid_cols]


# --------------------------------------------------------------------
# 主函数
# --------------------------------------------------------------------
def load_flight_foc_to_mysql(
    excel_path: Path | None = None,
    sheet_name: str | None = None,
) -> None:
    """
    读取 Excel Sheet ➜ 追加写入 flight_foc
    """
    excel_path = excel_path or _EXCEL_FILE
    sheet_name = sheet_name or _SHEET_NAME

    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # 1. 读取
    df = pd.read_excel(excel_path, sheet_name=sheet_name)

    # 去除列名首尾空格（防止“文件名　”这类情况）
    df.columns = df.columns.str.strip()

    # 2. 映射列名 + 清洗布尔列
    df = df.rename(columns=_COL_MAP)
    df = _clean_bool_fields(df)

    # 3. 保留数据库存在的列
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df = _filter_columns_to_table(df, eng)

    if df.empty:
        print("[WARN] 过滤后无有效列，已跳过写入。")
        return

    # 4. 写入
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",
        index=False,
        chunksize=2000,
        method="multi",
    )

    # 5. 反馈
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] flight_foc 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行。")


# --------------------------------------------------------------------
# CLI
# --------------------------------------------------------------------
if __name__ == "__main__":
    load_flight_foc_to_mysql()