# -*- coding: utf-8 -*-
"""
《库存情况.xlsx》Sheet1 ➜ MySQL 表 part_location_stock
(列方向基地 ⇒ 行方向记录)
"""

from __future__ import annotations
from pathlib import Path

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

_EXCEL_FILE: Path = DATA_DIR / "库存情况.xlsx"
_SHEET_NAME = "Sheet1"
_TABLE_NAME = "part_location_stock"


def load_inventory_location(excel_path: Path | None = None) -> None:
    """读取 Excel 并写入 part_location_stock"""
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(excel_path)

    # 1. 读取
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)
    if df.empty:
        print("[Warn] 读取结果为空，退出。")
        return

    df.columns = df.columns.str.strip()          # 去列名空白
    if "件号" not in df.columns:
        raise ValueError("缺少 '件号' 列")

    # 2. melt：列转行
    value_vars = [c for c in df.columns if c != "件号"]
    df_melt = df.melt(id_vars="件号", value_vars=value_vars,
                      var_name="location", value_name="qty")

    # 3. 清洗
    df_melt["pnr"] = df_melt["件号"].astype(str).str.strip()
    df_melt["location"] = df_melt["location"].astype(str).str.strip()
    df_melt["qty"] = (df_melt["qty"].astype(str)
                                 .str.replace("%", "", regex=False)
                                 .str.strip())
    df_melt["qty"] = pd.to_numeric(df_melt["qty"], errors="coerce").astype("Int64")
    df_melt = df_melt[df_melt["qty"].notna()]     # 去掉空数量

    df_final = df_melt[["pnr", "location", "qty"]]
    if df_final.empty:
        print("[Warn] 无有效数据可写。")
        return

    # 4. 写库
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df_final.to_sql(_TABLE_NAME, eng, if_exists="append",
                    index=False, chunksize=2000, method="multi")

    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] part_location_stock 导入完成，新增 {len(df_final)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    load_inventory_location()