# -*- coding: utf-8 -*-
"""
把《库存地类别.xlsx》导入 MySQL 表 location_category
------------------------------------------------
Excel → MySQL 字段映射见 _COL_MAP
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel ⇨ MySQL 字段映射
# --------------------------------------------------------------------
_EXCEL_FILE = DATA_DIR / "库存地类别.xlsx"
_TABLE_NAME = "location_category"

_COL_MAP: Dict[str, str] = {
    "库存地代码": "location_code",
    "库存地": "location_name",
    "库存地类别": "location_category",
}


def load_location_category_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取 Excel ➜ 写入 location_category
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ------------ 1. 读取首个 Sheet ------------
    df = pd.read_excel(excel_path, sheet_name=0, dtype=str)

    # ------------ 2. 列重命名 & 过滤 ------------
    df = df.rename(columns=_COL_MAP)
    keep_cols: List[str] = [col for col in _COL_MAP.values() if col in df.columns]
    df = df[keep_cols]

    # ------------ 3. 去重（按 location_code）------------
    df = df.drop_duplicates(subset=["location_code"])

    # ------------ 4. 写入数据库 ------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",   # 若需覆盖请改为 'replace'
        index=False,          # 不写入 DataFrame 行索引
        chunksize=1000,
        method="multi",
    )

    # ------------ 5. 反馈 ------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] location_category 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    load_location_category_to_mysql()