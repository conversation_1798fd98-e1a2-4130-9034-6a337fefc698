# -*- coding: utf-8 -*-
"""
《航材采购与库存风险分析报告.xlsx》 → 件号级风险统计汇总
Sheet : 件号级风险统计汇总  ➜ MySQL 表 part_risk_summary
"""

from __future__ import annotations
from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# ------------------------------------------------------------------
_EXCEL_FILE: Path = DATA_DIR / "航材采购与库存风险分析报告.xlsx"
_SHEET_NAME = "件号级风险统计汇总"
_TABLE_NAME = "part_risk_summary"
_MODEL_VERSION = 1            # excel 尚无字段，写死为 1
# ------------------------------------------------------------------

# Excel 列 → MySQL 字段
_COL_MAP: Dict[str, str] = {
    "件号":                     "pnr",
    "预测周期内缺件数量":         "forecast_missing_qty",
    "平均置信度":               "avg_confidence",
    "置信度标准差":             "std_confidence",
    "序号数量":                 "seq_count",
    "平均生存曲线数据量":         "avg_curve_points",
    "最小生存曲线数据量":         "min_curve_points",
    "最大生存曲线数据量":         "max_curve_points",
    "生存曲线数据量标准差":       "std_curve_points",
    "平均相似序号数":            "avg_similar_seq_cnt",
    "最小相似序号数":            "min_similar_seq_cnt",
    "最大相似序号数":            "max_similar_seq_cnt",
    "相似序号数标准差":          "std_similar_seq_cnt",
    "成功建立生存曲线数":         "succ_curve_cnt",
    "生存曲线成功率":            "succ_curve_rate",
    "数据量充足率":              "enough_data_rate",
    "相似序号充足率":            "enough_similar_seq_rate",
    "KM生存曲线质量评分":         "km_quality_score",
    "件号返修数":               "repair_cnt",
    "预估周期内返修与故障偏差量":   "fx_fc_bias_qty",
    "预估周期内返修航材保障率":     "fx_mat_guarantee_rate",
    "当前可用库自有器材数量":      "current_own_stock_qty",
    "预估周期内自有航材保障率":     "own_mat_guarantee_rate",
    "航材目标保障率":            "target_guarantee_rate",
    "达成保障率需预留安全库存数量": "safe_stock_need_qty",
    "KM数据质量不确定性因子":      "km_uncertainty_factor",
    "可外租库存数量":            "rentable_stock_qty",
    "安全库存利用率":            "safe_stock_usage_rate",
}

# 整型列
_INT_COLS: List[str] = [
    "forecast_missing_qty", "seq_count",
    "min_curve_points", "max_curve_points",
    "min_similar_seq_cnt", "max_similar_seq_cnt",
    "succ_curve_cnt", "repair_cnt",
    "fx_fc_bias_qty", "current_own_stock_qty",
    "safe_stock_need_qty", "rentable_stock_qty"
]
# 浮点列（百分比/评分等）
_FLOAT_COLS: List[str] = [
    c for c in _COL_MAP.values()
    if c not in ("pnr",) + tuple(_INT_COLS)
]

# ------------------------------------------------------------------
def _clean_percent(series: pd.Series) -> pd.Series:
    """
    将形如 '98.5%' -> '98.5'，便于后续 to_numeric
    """
    return series.str.replace('%', '', regex=False).str.strip()


def load_part_risk_summary(excel_path: Path | None = None) -> None:
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(excel_path)

    # 读取
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)

    # 列映射
    df.columns = df.columns.str.strip()
    lower_map = {k.lower(): v for k, v in _COL_MAP.items()}
    df = df.rename(columns=lambda c: lower_map.get(c.lower(), c))
    df = df[[v for v in _COL_MAP.values() if v in df.columns]]

    if df.empty:
        print("[Warn] 读取结果为空，已退出。")
        return

    #  百分号清洗
    for col in _FLOAT_COLS:
        if col in df.columns:
            df[col] = _clean_percent(df[col])

    #  类型转换
    for col in _INT_COLS:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce").astype("Int64")
    for col in _FLOAT_COLS:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce")

    # 添加模型版本号
    df["model_version"] = _MODEL_VERSION

    # 写库
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(_TABLE_NAME,
              con=eng,
              if_exists="append",
              index=False,
              chunksize=2000,
              method="multi")

    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] part_risk_summary 导入完成，新增 {len(df)} 行，总记录 {total} 行.")


# ------------------------------------------------------------------
if __name__ == "__main__":
    load_part_risk_summary()