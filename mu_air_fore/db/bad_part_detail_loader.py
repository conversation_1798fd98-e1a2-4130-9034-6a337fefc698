# -*- coding: utf-8 -*-
"""
把《航材采购与库存风险分析报告.xlsx》的【坏件细节】Sheet
写入 MySQL 表 bad_part_detail
"""

from __future__ import annotations
from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# ------------------------------------------------------------------
# 基本常量
# ------------------------------------------------------------------
_EXCEL_FILE: Path = DATA_DIR / "航材采购与库存风险分析报告.xlsx"
_SHEET_NAME = "坏件细节"
_TABLE_NAME = "bad_part_detail"

# Excel → MySQL 字段映射
_COL_MAP: Dict[str, str] = {
    "件号":                     "pnr",
    "序号":                     "seq",
    "预测TSR":                  "predict_tsr",
    "置信度":                   "confidence",
    "模型版本":                 "model_version",
    "总维修次数":               "total_repair_cnt",
    "KM样本量警告":             "km_sample_warn",
    "生存曲线建立状态":          "survival_curve_status",
    "生存曲线数据量":            "curve_points",
    "相似序号数目":             "similar_seq_cnt",
    "机号":                     "aircraft_no",
    "最后排班日期":             "last_schedule_date",
    "执管基地":                 "operate_base",
    "过夜基地":                 "overnight_base",
    "TAT周期":                  "tat_period",
    "TAT周期内的飞行时长":       "flight_hours_in_tat",
    "已使用时长":               "used_hours",
    "排班结束前飞行时长":        "flight_hours_before_end",
    "本TAT周期前剩余TSR":        "tsr_before_tat",
    "tag":                      "tag",
    "本TAT周期后剩余TSR":        "tsr_after_tat",
    "是否故障":                 "is_fault",
    "排班结束TSR":              "tsr_end_schedule",
    "基地":                     "base",
}

# 列类型划分
_INT_COLS: List[str] = [
    "total_repair_cnt", "curve_points", "similar_seq_cnt", "tat_period"
]
_DATE_COLS: List[str] = ["last_schedule_date"]
_FLOAT_COLS: List[str] = [
    c for c in _COL_MAP.values()
    if c not in {"pnr", "seq", "km_sample_warn", "survival_curve_status",
                 "aircraft_no", "operate_base", "overnight_base",
                 "tag", "base"} | set(_INT_COLS) | set(_DATE_COLS)
]


# ------------------------------------------------------------------
def load_bad_part_detail(excel_path: Path | None = None) -> None:
    """读取 Excel → 写入 bad_part_detail"""
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(excel_path)

    # 读取
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)

    # 列重命名（忽略大小写、去首尾空格）
    df.columns = df.columns.str.strip()
    lower_map = {k.lower(): v for k, v in _COL_MAP.items()}
    df = df.rename(columns=lambda c: lower_map.get(c.lower(), c))

    # 仅保留映射列
    df = df[[v for v in _COL_MAP.values() if v in df.columns]]

    if df.empty:
        print("[Warn] 读取结果为空，退出写库。")
        return

    # 类型转换
    for col in _INT_COLS:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce").astype("Int64")

    for col in _FLOAT_COLS:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce")

    for col in _DATE_COLS:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors="coerce")

    # 布尔列映射为 0/1
    if "is_fault" in df.columns:
        df["is_fault"] = df["is_fault"].map(
            lambda x: 1 if str(x).strip() in {"1", "是", "Y", "y",
                                              "true", "True"} else 0)

    # 写入 MySQL
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(_TABLE_NAME,
              con=eng,
              if_exists="append",
              index=False,
              chunksize=2000,
              method="multi")

    # 反馈
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] bad_part_detail 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行.")


# ------------------------------------------------------------------
if __name__ == "__main__":
    load_bad_part_detail()