# -*- coding: utf-8 -*-
"""
把 Excel 直接批量导入 repair_contracts 表
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import numpy as np
import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

_COL_MAP: Dict[str, str] = {
    "件号": "pnr",
    "物料组": "material_grp",
    "序号": "seq",
    "出库日期": "outbound_date",
    "件号描述": "part_desc",
    "批次": "batch_no",
    "出库指令单": "outbound_doc",
    "合同出库库存地": "out_stock_loc",
    "国内发出运单号": "dom_awb",
    "状态": "status",
    "合同类型": "contract_type",
    "合同号": "contract_no",
    "合同库存地": "contract_stock_loc",
    "合同主控单位": "contract_unit",
    "合同创建人": "contract_creator",
    "TSR": "tsr",
    "CSR": "csr",
    "供应商": "supplier",
    "维修级别": "repair_level",
    "报价": "price",
    "货币": "currency",
    "入库日期": "inbound_date",
    "合同日期": "contract_date",
    "机号": "aircraft_no",
    "机型": "aircraft_type",
    "选择": "flag_select",
    "主合同是否关闭": "main_closed",
    "转场报废信息": "scrap_info",
    "计划交货期": "plan_delivery",
    "拆下日期": "removal_date",
    "系统拆下日期": "sys_removal_date",
    "退库日期": "return_stock_date",
    "退料上架日期": "return_putaway",
    "检验决策日期": "inspect_date",
    "评估单日期": "evaluate_date",
    "计划审批单": "plan_doc",
    "创建人": "creator",
    "优先级": "priority",
    "航班号": "flight_no",
    "航班日期": "flight_date",
    "国际发出运单号": "intl_awb",
    "航班号.1": "flight_no2",
    "航班日期.1": "flight_date2",
    "厂家接收到货物日期": "mfr_recv_date",
    "厂家报价日期": "mfr_quote_date",
    "报价确认日期": "quote_confirm_date",
    "厂家发出日期": "mfr_ship_date",
    "返回运单号": "return_awb",
    "航班号.2": "flight_no3",
    "航班日期.2": "flight_date3",
    "转航班号": "transfer_flight_no",
    "转航班日期": "transfer_flight_dt",
    "进口日期": "import_date",
    "放行日期": "release_date",
    "转运运单号": "trans_awb",
    "航班号.3": "flight_no4",
    "航班日期.3": "flight_date4",
    "签收日期": "sign_date",
    "原装机件索赔": "orig_claim",
    "索赔成功": "claim_success",
    "拒赔原因": "claim_reason",
    "拒赔备注": "claim_remark",
    "是否NFF": "nff_flag",
    "付款原因": "pay_reason",
    "合同跟踪情况": "contract_track",
    "合同跟踪情况记录": "contract_track_rec",
    "改装后件号": "mod_part_no",
    "POOLING件送修": "pooling_flag",
    "差异通知单号": "var_notice_no",
    "差异单创建日期": "var_notice_create",
    "差异单关闭日期": "var_notice_close",
    "拆下发动机/APU型号": "engine_model",
    "拆下发动机/APU序号": "engine_sn",
    "ETOPS": "etops_flag",
    "关键部件": "critical_part",
    "慢流转航材": "slow_material",
    "航材重要性": "mat_importance",
    "收货件号": "recv_part_no",
    "收货序号": "recv_serial_no",
    "报价单查看": "quote_view",
    "TSN": "tsn",
    "CSN": "csn",
    "DSN": "dsn",
    "TSO": "tso",
    "CSO": "cso",
    "DSO": "dso",
    "DSR": "dsr",
    "TST": "tst",
    "CST": "cst",
    "DST": "dst",
    "进出口合同号": "impex_contract_no",
}

# 需要转换为 0/1 的布尔（INT）字段
_BOOL_FIELDS: List[str] = [
    "flag_select",
    "main_closed",
    "orig_claim",
    "claim_success",
    "nff_flag",
    "pooling_flag",
    "etops_flag",
    "critical_part",
    "slow_material",
]

_EXCEL_FILE = DATA_DIR / "送修合同清单TSR_CSR更正.xlsx"
_TABLE_NAME = "repair_contracts"


def _normalize_bool(df: pd.DataFrame) -> pd.DataFrame:
    """
    将布尔或“是/否”“TRUE/FALSE”等字符映射为 1/0
    """
    true_set = {"是", "Y", "YES", "TRUE", "1"}
    false_set = {"否", "N", "NO", "FALSE", "0", ""}

    for col in _BOOL_FIELDS:
        if col not in df.columns:
            continue
        df[col] = (
            df[col]
            .astype(str)
            .str.strip()
            .str.upper()
            .map(lambda x: 1 if x in true_set else (0 if x in false_set else np.nan))
            .astype("Int64")
        )
    return df


def load_repair_contracts_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取 Excel ➜ 清洗 ➜ 写入 repair_contracts
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # --- 1. 读取 Excel ---
    df = pd.read_excel(excel_path, sheet_name="Sheet1")

    # --- 2. 列重命名 & 提取 ---
    df = df.rename(columns=_COL_MAP)

    # 只保留映射中已声明且确实存在的列（避免形状不一致报错）
    keep_cols = [v for v in _COL_MAP.values() if v in df.columns]
    df = df[keep_cols]

    # --- 3. 类型与布尔字段处理 ---
    df = _normalize_bool(df)

    # --- 4. 写入 MySQL ---
    eng = create_engine(SQLALCHEMY_URL, echo=False)

    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",
        index=False,
        chunksize=2_000,
        method="multi",
    )

    # --- 5. 结果反馈 ---
    with eng.begin() as conn:
        count = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] 导入完成，新增 {len(df)} 行； 当前表总记录 {count} 行。")