# -*- coding: utf-8 -*-
"""
把 “修理次数14_长表.xlsx” 导入 repair_history 表
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel -> MySQL 列映射
# --------------------------------------------------------------------
_COL_MAP: Dict[str, str] = {
    "件号": "pnr",
    "序号": "seq",
    "合同类型": "contract_type",
    "供应商": "supplier",
    "维修级别": "repair_level",
    "报价": "quotation",
    "TSN": "tsn",
    "CSN": "csn",
    "TSR": "tsr",
    "CSR": "csr",
    "合同日期": "contract_datetime",
    "状态": "status",
    "合同号": "contract_no",
    "是否补TSR": "tsr_replenished",
    "维修次数": "repair_count",
    "组异常类型": "group_exception_type",
    "第几次维修": "repair_index",
    "单条记录异常类型": "record_exception_type",
}

_EXCEL_FILE = DATA_DIR / "修理次数14_长表.xlsx"
_TABLE_NAME = "repair_history"

_BOOL_FIELD = "tsr_replenished"


def _clean_bool_field(df: pd.DataFrame) -> pd.DataFrame:

    if _BOOL_FIELD in df.columns:
        df[_BOOL_FIELD] = df[_BOOL_FIELD].astype(str).str.strip().str.upper()
    return df


def load_repair_history_to_mysql(excel_path: Path | None = None) -> None:
    """
    读取长表 Excel ➜ 写入 repair_history
    """
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ------------ 1. 读取 Excel ------------
    df = pd.read_excel(excel_path, sheet_name="完整维修履历")

    # ------------ 2. 列重命名 & 过滤 ------------
    df = df.rename(columns=_COL_MAP)
    keep_cols: List[str] = [v for v in _COL_MAP.values() if v in df.columns]
    df = df[keep_cols]

    # ------------ 3. 清理布尔列（保留原值） ------------
    df = _clean_bool_field(df)

    # ------------ 4. 写入数据库 ------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",
        index=False,
        chunksize=2000,
        method="multi",
    )

    # ------------ 5. 反馈 ------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(f"[DB] repair_history 导入完成，新增 {len(df)} 行；当前表总记录 {total} 行。")


if __name__ == "__main__":
    load_repair_history_to_mysql()