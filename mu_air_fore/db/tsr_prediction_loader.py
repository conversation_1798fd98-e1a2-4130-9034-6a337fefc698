# -*- coding: utf-8 -*-
"""
把《航材采购与库存风险分析报告.xlsx》的【各序号风险计算详情】Sheet
写入 MySQL 表 tsr_prediction_res

Excel → MySQL 字段映射见 _COL_MAP
timeline  → tsr
survival_probability → survival_probability
model_version 固定写 1
"""

from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL
from mu_air_fore.config.settings import DATA_DIR

# --------------------------------------------------------------------
# Excel ⇨ MySQL 基本信息
# --------------------------------------------------------------------
_EXCEL_FILE = DATA_DIR / "航材采购与库存风险分析报告.xlsx"
_SHEET_NAME = "KM生存曲线数据"
_TABLE_NAME = "tsr_prediction_res"

# Excel 原列名 → MySQL 字段
_COL_MAP: Dict[str, str] = {
    "件号": "pnr",
    "序号": "seq",
    "timeline": "tsr",
    "survival_probability": "survival_probability",
}

_MODEL_VERSION = 1  # 固定写 1


def _standardize_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    1. 去除列名首尾空白
    2. 不区分大小写做映射
    """
    df.columns = df.columns.str.strip()

    # 构造大小写无关的映射 dict
    lower_map = {k.lower(): v for k, v in _COL_MAP.items()}
    df = df.rename(columns=lambda c: lower_map.get(c.lower(), c))
    return df


def load_tsr_prediction_to_mysql(excel_path: Path | None = None) -> None:
    """读取 Excel ➜ 追加写入 tsr_prediction_res"""
    excel_path = excel_path or _EXCEL_FILE
    if not excel_path.exists():
        raise FileNotFoundError(f"Excel 文件不存在：{excel_path}")

    # ---------------- 1. 读取 ----------------
    df = pd.read_excel(excel_path, sheet_name=_SHEET_NAME, dtype=str)

    # ---------------- 2. 列重命名 ----------------
    df = _standardize_columns(df)
    keep_cols: List[str] = [v for v in _COL_MAP.values() if v in df.columns]
    df = df[keep_cols]

    if df.empty:
        print("[Warn] 读取结果为空，已退出。")
        return

    # ---------------- 3. 类型转换 ----------------
    for col in ("tsr", "survival_probability"):
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce")

    # ---------------- 4. 模型版本 ----------------
    df["model_version"] = _MODEL_VERSION

    # ---------------- 5. 写库 ----------------
    eng = create_engine(SQLALCHEMY_URL, echo=False)
    df.to_sql(
        name=_TABLE_NAME,
        con=eng,
        if_exists="append",
        index=False,
        chunksize=2000,
        method="multi",
    )

    # ---------------- 6. 反馈 ----------------
    with eng.begin() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()
    print(
        f"[DB] tsr_prediction_res 导入完成：新增 {len(df)} 行；"
        f"当前表总记录 {total} 行."
    )


if __name__ == "__main__":
    load_tsr_prediction_to_mysql()