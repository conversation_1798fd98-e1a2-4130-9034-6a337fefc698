# -*- coding: utf-8 -*-
"""
《相似度分析结果表格_V1.0.xlsx》 ➜ MySQL 表 km_pn_list
加载KM相似件序号数据
"""

from __future__ import annotations
from pathlib import Path
import argparse
import sys

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL

# --------------------------------------------------------------------
# 配置参数
# --------------------------------------------------------------------

_TABLE_NAME = "km_pn_list"

# 获取项目根目录（mu-air-fore目录）
_PROJECT_ROOT = Path(__file__).resolve().parents[2]  # 从 mu_air_fore/db/ 向上两级到项目根目录

# 尝试多个可能的KM输出目录路径
_POSSIBLE_KM_DIRS = [
    _PROJECT_ROOT / "model" / "modeloutput" / "KM_Curve_similarity",  # 原始路径
    Path("/home/<USER>/mu_air_python/mu_air_python/model/modeloutput/KM_Curve_similarity"),  # 服务器路径1
    Path("/home/<USER>/mu_air_python/model/modeloutput/KM_Curve_similarity"),  # 服务器路径2
    Path.cwd() / "model" / "modeloutput" / "KM_Curve_similarity",  # 当前工作目录
]

# Excel列名到数据库字段的映射
_COL_MAP = {
    "目标件号": "pn",
    "目标序号": "seq",
    "相似序号": "be_similar_seq",
    "相似度": "similarity",
    "数据量": "data_count"
}


def find_km_excel_file() -> Path | None:
    """
    查找KM相似度分析结果Excel文件

    Returns:
        Excel文件路径，如果找不到返回None
    """
    print(f"当前工作目录：{Path.cwd()}")
    print(f"项目根目录：{_PROJECT_ROOT}")

    # 尝试多个可能的KM输出目录
    km_output_dir = None
    for possible_dir in _POSSIBLE_KM_DIRS:
        print(f"尝试路径：{possible_dir}")
        if possible_dir.exists():
            km_output_dir = possible_dir
            print(f"✅ 找到KM输出目录：{km_output_dir}")
            break
        else:
            print(f"❌ 路径不存在：{possible_dir}")

    if km_output_dir is None:
        print(f"错误：所有可能的KM输出目录都不存在")
        print(f"尝试的路径：")
        for path in _POSSIBLE_KM_DIRS:
            print(f"  - {path}")
        print(f"请确保已运行KM相似度分析并生成了输出文件")
        return None

    # 查找包含时间戳的目录
    matching_dirs = []
    for item in km_output_dir.iterdir():
        if item.is_dir() and "件序号相似度分析_" in item.name:
            matching_dirs.append(item)

    if not matching_dirs:
        print(f"错误：未找到件序号相似度分析目录")
        print(f"在目录 {km_output_dir} 中查找包含 '件序号相似度分析_' 的子目录")
        # 显示目录中的所有内容
        print(f"目录内容：")
        try:
            for item in km_output_dir.iterdir():
                print(f"  - {item.name} ({'目录' if item.is_dir() else '文件'})")
        except Exception as e:
            print(f"  无法列出目录内容：{e}")
        return None

    # 如果有多个匹配，选择最新的（按目录名排序，时间戳在最后）
    latest_dir = sorted(matching_dirs, key=lambda x: x.name)[-1]
    print(f"找到KM分析目录：{latest_dir}")

    # 查找Excel文件
    excel_file = latest_dir / "相似度分析结果表格_V1.0.xlsx"
    if not excel_file.exists():
        print(f"错误：Excel文件不存在：{excel_file}")
        # 显示目录中的所有Excel文件
        print(f"目录中的文件：")
        try:
            for item in latest_dir.iterdir():
                if item.is_file():
                    print(f"  - {item.name}")
        except Exception as e:
            print(f"  无法列出目录内容：{e}")
        return None

    return excel_file


def load_km_pn_list(excel_path: Path | None = None) -> bool:
    """
    读取KM相似度分析Excel文件并写入km_pn_list表

    Args:
        excel_path: Excel文件路径，为None时自动查找

    Returns:
        bool: 成功返回True，失败返回False
    """
    # 查找Excel文件
    if excel_path is None:
        excel_path = find_km_excel_file()
        if excel_path is None:
            return False

    if not excel_path.exists():
        print(f"错误：Excel文件不存在：{excel_path}")
        return False

    print(f"开始加载文件：{excel_path}")

    try:
        # 1. 读取Excel文件
        df = pd.read_excel(excel_path, dtype=str)
        print(f"原始数据行数：{len(df)}")

        if df.empty:
            print("警告：Excel文件为空")
            return False

        # 去除列名空白
        df.columns = df.columns.str.strip()
        print(f"Excel列名：{list(df.columns)}")

        # 2. 检查必需的列
        required_cols = list(_COL_MAP.keys())
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"错误：缺少必需的列：{missing_cols}")
            print(f"期望的列名：{required_cols}")
            return False

        # 3. 列重命名
        df = df.rename(columns=_COL_MAP)

        # 4. 选择需要的列
        keep_cols = list(_COL_MAP.values())
        df = df[keep_cols]

        # 5. 数据清洗
        # 去除字符串字段的空白
        string_cols = ['pn', 'seq', 'be_similar_seq']
        for col in string_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # 处理相似度字段
        if 'similarity' in df.columns:
            df['similarity'] = df['similarity'].astype(str).str.strip()
            # 如果相似度是数值，可以进行范围检查
            # df['similarity'] = pd.to_numeric(df['similarity'], errors='coerce')

        # 处理数据量字段
        if 'data_count' in df.columns:
            df['data_count'] = pd.to_numeric(df['data_count'], errors='coerce').fillna(0).astype(int)

        # 添加km_threshold字段
        df['km_threshold'] = df['similarity']

        # 6. 删除空值行
        df = df.dropna(subset=['pn', 'seq', 'be_similar_seq'])
        print(f"清洗后数据行数：{len(df)}")

        if df.empty:
            print("警告：没有有效数据可以导入")
            return False

        # 7. 数据统计信息
        print("\n数据统计信息：")
        print(f"  唯一件号数量: {df['pn'].nunique()}")
        print(f"  唯一目标序号数量: {df['seq'].nunique()}")
        print(f"  唯一相似序号数量: {df['be_similar_seq'].nunique()}")

        # 显示前几行数据样例
        print("\n数据样例（前5行）：")
        print(df.head().to_string(index=False))
        print()

        # 8. 写入数据库
        print("开始写入数据库...")
        eng = create_engine(SQLALCHEMY_URL, echo=False)

        # 注意：由于表有自增主键id，需要排除id字段
        df_final = df[['pn', 'seq', 'be_similar_seq', 'similarity', 'data_count', 'km_threshold']]

        df_final.to_sql(
            name=_TABLE_NAME,
            con=eng,
            if_exists="append",
            index=False,
            chunksize=2000,
            method="multi"
        )

        # 9. 反馈结果
        with eng.begin() as conn:
            total = conn.execute(text(f"SELECT COUNT(*) FROM `{_TABLE_NAME}`")).scalar()

        print(f"[DB] {_TABLE_NAME} 导入完成，新增 {len(df_final)} 行；当前表总记录 {total} 行.")
        return True

    except Exception as e:
        print(f"错误：处理文件时发生异常：{e}")
        import traceback
        traceback.print_exc()
        return False


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='加载KM相似件序号数据到MySQL')
    parser.add_argument(
        '--file', '-f',
        type=str,
        help='指定Excel文件路径（可选，不指定则自动查找）'
    )
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    print(f"=== KM相似件序号数据加载器 ===")
    print(f"目标表: {_TABLE_NAME}")

    if args.file:
        excel_path = Path(args.file)
        print(f"指定文件: {excel_path}")
    else:
        excel_path = None
        print("自动查找文件")

    print("=" * 40)

    success = load_km_pn_list(excel_path)
    if success:
        print("✅ 数据加载完成")
    else:
        print("❌ 数据加载失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
