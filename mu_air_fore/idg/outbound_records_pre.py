# -*- coding: utf-8 -*-
"""
出库记录预处理
1) 出库类型=生产发料  → 按【过帐日期+领料申请号+序号】分组，数量求和
2) 其它类型           → 保留原始行
输出: data/出库记录all_预处理.xlsx
"""
from __future__ import annotations
import os
from pathlib import Path
import pandas as pd
from mu_air_fore.config.settings import DATA_DIR

INPUT_FILE  = DATA_DIR / "出库记录all.xlsx"
OUTPUT_FILE = DATA_DIR / "出库记录all_预处理.xlsx"
SHEET_IN, SHEET_OUT = 0, "汇总"

def preprocess_outbound() -> Path:
    df = pd.read_excel(INPUT_FILE, sheet_name=SHEET_IN, dtype=str)

    df.columns = df.columns.str.strip()
    df["过帐日期"] = pd.to_datetime(df["过帐日期"], errors="coerce")
    df["数量"]     = pd.to_numeric(df["数量"], errors="coerce").fillna(0)

    df_prod  = df[df["出库类型"] == "生产发料"].copy()
    df_other = df[df["出库类型"] != "生产发料"].copy()

    if not df_prod.empty:
        grp_cols = ["过帐日期", "领料申请号", "序号"]
        agg = {c: "first" for c in df_prod.columns if c not in grp_cols + ["数量"]}
        agg["数量"] = "sum"
        df_prod = (df_prod.groupby(grp_cols, dropna=False).agg(agg).reset_index())

    result = pd.concat([df_prod, df_other], ignore_index=True)
    result.sort_values(["过帐日期", "领料申请号", "序号"], inplace=True)

    OUTPUT_FILE.parent.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(OUTPUT_FILE, engine="openpyxl") as w:
        result.to_excel(w, sheet_name=SHEET_OUT, index=False)

    print("[Outbound] 已生成：", os.path.abspath(OUTPUT_FILE))
    return OUTPUT_FILE

if __name__ == "__main__":
    preprocess_outbound()