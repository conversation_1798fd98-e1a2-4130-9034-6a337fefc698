# -*- coding: utf-8 -*-
"""
把 IDG 履历关联到出库记录，生成 data/IDG履历_关联出库.xlsx
"""
from __future__ import annotations

import os
from pathlib import Path
from datetime import timedelta

import pandas as pd

from mu_air_fore.config.settings import DATA_DIR

# ---------- 路径 ----------
# IDG_FILE = DATA_DIR / "IDG及发动机拆换_截至2025_IDG履历.xlsx"
IDG_FILE = DATA_DIR / "航材装机履历.xlsx"
OUT_FILE = DATA_DIR / "出库记录all_预处理.xlsx"
OUTPUT   = DATA_DIR / "航材出库履历.xlsx"

IDG_SHEET  = "IDG履历"
OUT_SHEET  = 0                       # 首张表

# ---------- 列名 ----------
IDG_TIME  = "时间"
IDG_PN1   = "idg1件号"
IDG_SN1   = "idg1"
IDG_PN2   = "idg2件号"
IDG_SN2   = "idg2"
IDG_MK1   = "idg1变化标记"
IDG_MK2   = "idg2变化标记"

OUT_DATE  = "过帐日期"
OUT_PN    = "件号"
OUT_SN    = "序号"
OUT_DOC   = "物料凭证"
OUT_USE   = "领料用途"
OUT_TYPE  = "出库类型"

PLACEHOLDER = "xxx"
MAX_DIFF = timedelta(days=2)

def _concat(series: pd.Series) -> str:
    return ",".join(series.astype(str).dropna().unique())

def link_idg_outbound() -> Path:
    idg = pd.read_excel(IDG_FILE, sheet_name=IDG_SHEET, dtype=str)
    out = pd.read_excel(OUT_FILE, sheet_name=OUT_SHEET, dtype=str)

    # 清洗
    idg[IDG_TIME]  = pd.to_datetime(idg[IDG_TIME])
    out[OUT_DATE]  = pd.to_datetime(out[OUT_DATE], errors="coerce")
    out.columns    = out.columns.astype(str).str.strip().str.replace("\u3000", "")

    # 仅生产发料
    out = out[out[OUT_TYPE] == "生产发料"].copy()

    # 初始化目标列
    for c in ("idg1过账日期","idg1物料凭证","idg1领料用途",
              "idg2过账日期","idg2物料凭证","idg2领料用途"):
        idg[c] = ""

    def _process(idx: int):
        sn_col, pn_col, mark_col = (IDG_SN1, IDG_PN1, IDG_MK1) if idx==1 else (IDG_SN2, IDG_PN2, IDG_MK2)
        tgt_cols = (f"idg{idx}过账日期", f"idg{idx}物料凭证", f"idg{idx}领料用途")

        mask = idg[mark_col] == "是"
        if not mask.any():
            return
        part_df = (idg.loc[mask,[IDG_TIME,pn_col,sn_col]]
                     .merge(out, left_on=[pn_col,sn_col], right_on=[OUT_PN,OUT_SN], how="left"))
        diff = part_df[IDG_TIME] - part_df[OUT_DATE]
        part_df = part_df[(diff>=timedelta(0)) & (diff<=MAX_DIFF)]

        grp = (part_df.groupby([IDG_TIME,pn_col,sn_col])
                      .agg({OUT_DATE:_concat, OUT_DOC:_concat, OUT_USE:_concat}))
        for key, row in grp.iterrows():
            sel = (idg[IDG_TIME]==key[0]) & (idg[pn_col]==key[1]) & (idg[sn_col]==key[2])
            idg.loc[sel, tgt_cols[0]] = row[OUT_DATE]
            idg.loc[sel, tgt_cols[1]] = row[OUT_DOC]
            idg.loc[sel, tgt_cols[2]] = row[OUT_USE]

    _process(1)
    _process(2)

    with pd.ExcelWriter(OUTPUT, engine="openpyxl") as w:
        idg.to_excel(w, sheet_name="关联结果", index=False)
    print(f"[IDG] 出库记录已关联：{os.path.abspath(OUTPUT)}")
    return OUTPUT

if __name__ == "__main__":
    link_idg_outbound()