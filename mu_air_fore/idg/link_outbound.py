# -*- coding: utf-8 -*-
"""
把 IDG 履历关联到出库记录，生成 data/IDG履历_关联出库.xlsx
"""
from __future__ import annotations

import os
from pathlib import Path
from datetime import timedelta

import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.settings import DATA_DIR
from mu_air_fore.config.db_settings import SQLALCHEMY_URL

# ---------- 路径 ----------
# IDG_FILE = DATA_DIR / "IDG及发动机拆换_截至2025_IDG履历.xlsx"
IDG_FILE = DATA_DIR / "航材装机履历.xlsx"
OUT_FILE = DATA_DIR / "出库记录all_预处理.xlsx"
OUTPUT   = DATA_DIR / "航材出库履历.xlsx"

IDG_SHEET  = "IDG履历"
OUT_SHEET  = 0                       # 首张表

# ---------- 列名 ----------
IDG_TIME  = "时间"
IDG_PN1   = "idg1件号"
IDG_SN1   = "idg1"
IDG_PN2   = "idg2件号"
IDG_SN2   = "idg2"
IDG_MK1   = "idg1变化标记"
IDG_MK2   = "idg2变化标记"

OUT_DATE  = "过帐日期"
OUT_PN    = "件号"
OUT_SN    = "序号"
OUT_DOC   = "物料凭证"
OUT_USE   = "领料用途"
OUT_TYPE  = "出库类型"

PLACEHOLDER = "xxx"
MAX_DIFF = timedelta(days=2)

def _concat(series: pd.Series) -> str:
    return ",".join(series.astype(str).dropna().unique())

def save_idg_history_to_mysql(idg_df: pd.DataFrame) -> None:
    """
    将IDG履历数据写入 MySQL idg_history 表

    参数:
        idg_df: 包含IDG履历数据的 DataFrame
    """
    # 创建数据库连接
    engine = create_engine(SQLALCHEMY_URL, echo=False)

    # 复制数据框以避免修改原始数据
    df_to_save = idg_df.copy()

    # 字段映射 - 将中文列名映射到数据库字段名
    column_mapping = {
        '机号': 'aircraft_no',
        '时间': 'record_time',
        'idg1件号': 'idg1_pnr',
        'idg1': 'idg1_seq',
        'idg2件号': 'idg2_pnr',
        'idg2': 'idg2_seq',
        '原因': 'reason',
        'idg1变化标记': 'idg1_change_flag',
        'idg2变化标记': 'idg2_change_flag',
        'idg1过账日期': 'idg1_post_datetime',
        'idg1物料凭证': 'idg1_material_doc',
        'idg1领料用途': 'idg1_issue_purpose',
        'idg2过账日期': 'idg2_post_datetime',
        'idg2物料凭证': 'idg2_material_doc',
        'idg2领料用途': 'idg2_issue_purpose'
    }

    # 重命名列
    df_to_save.rename(columns=column_mapping, inplace=True)

    # 选择需要保存到数据库的列
    columns_to_save = list(column_mapping.values())
    # 确保只选择存在的列
    columns_to_save = [col for col in columns_to_save if col in df_to_save.columns]
    df_to_save = df_to_save[columns_to_save]

    # 先清空表中的所有数据
    with engine.begin() as conn:
        conn.execute(text("TRUNCATE TABLE [idg_history](file://D:\code\mu_air_python\mu_air_fore\idg\idg_history.py#L0-L0)"))

    # 写入数据库
    df_to_save.to_sql(
        name='idg_history',
        con=engine,
        if_exists='append',
        index=False,
        chunksize=2000,
        method='multi'
    )

    # 输出反馈信息
    with engine.begin() as conn:
        total = conn.execute(text("SELECT COUNT(*) FROM [idg_history](file://D:\code\mu_air_python\mu_air_fore\idg\idg_history.py#L0-L0)")).scalar()
    print(f"[DB] idg_history 导入完成，新增 {len(df_to_save)} 行；当前表总记录 {total} 行.")

def link_idg_outbound() -> Path:
    idg = pd.read_excel(IDG_FILE, sheet_name=IDG_SHEET, dtype=str)
    out = pd.read_excel(OUT_FILE, sheet_name=OUT_SHEET, dtype=str)

    # 清洗
    idg[IDG_TIME]  = pd.to_datetime(idg[IDG_TIME])
    out[OUT_DATE]  = pd.to_datetime(out[OUT_DATE], errors="coerce")
    out.columns    = out.columns.astype(str).str.strip().str.replace("\u3000", "")

    # 仅生产发料
    out = out[out[OUT_TYPE] == "生产发料"].copy()

    # 初始化目标列
    for c in ("idg1过账日期","idg1物料凭证","idg1领料用途",
              "idg2过账日期","idg2物料凭证","idg2领料用途"):
        idg[c] = ""

    def _process(idx: int):
        sn_col, pn_col, mark_col = (IDG_SN1, IDG_PN1, IDG_MK1) if idx==1 else (IDG_SN2, IDG_PN2, IDG_MK2)
        tgt_cols = (f"idg{idx}过账日期", f"idg{idx}物料凭证", f"idg{idx}领料用途")

        mask = idg[mark_col] == "是"
        if not mask.any():
            return
        part_df = (idg.loc[mask,[IDG_TIME,pn_col,sn_col]]
                     .merge(out, left_on=[pn_col,sn_col], right_on=[OUT_PN,OUT_SN], how="left"))
        diff = part_df[IDG_TIME] - part_df[OUT_DATE]
        part_df = part_df[(diff>=timedelta(0)) & (diff<=MAX_DIFF)]

        grp = (part_df.groupby([IDG_TIME,pn_col,sn_col])
                      .agg({OUT_DATE:_concat, OUT_DOC:_concat, OUT_USE:_concat}))
        for key, row in grp.iterrows():
            sel = (idg[IDG_TIME]==key[0]) & (idg[pn_col]==key[1]) & (idg[sn_col]==key[2])
            idg.loc[sel, tgt_cols[0]] = row[OUT_DATE]
            idg.loc[sel, tgt_cols[1]] = row[OUT_DOC]
            idg.loc[sel, tgt_cols[2]] = row[OUT_USE]

    _process(1)
    _process(2)

    with pd.ExcelWriter(OUTPUT, engine="openpyxl") as w:
        idg.to_excel(w, sheet_name="关联结果", index=False)
    print(f"[IDG] 出库记录已关联：{os.path.abspath(OUTPUT)}")

    # 保存到MySQL数据库
    save_idg_history_to_mysql(idg)

    return OUTPUT

if __name__ == "__main__":
    link_idg_outbound()
