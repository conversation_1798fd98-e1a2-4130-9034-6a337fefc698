# -*- coding: utf-8 -*-
"""
基于 3-sheet(已补发动机 IDG) 生成完整 IDG 拆装履历
输出: data/IDG及发动机拆换_截至2025_IDG履历.xlsx
"""
from __future__ import annotations
import os
from datetime import timedelta
from pathlib import Path

import pandas as pd

from mu_air_fore.config.settings import DATA_DIR

# ---------------- 路径 & 常量 ----------------
INPUT_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_简化_3sheet_补发动机IDG.xlsx"
OUTPUT_FILE = DATA_DIR / "IDG及发动机拆换_截至2025_IDG履历.xlsx"
SHEET_NAME  = "飞机-idg"

TIME_COL, FLAG_COL = "拆换日期", "拆装标记"
SN_COL,   PART_COL = "序号",       "件号"
PLANE_COL          = "机号"

DELTA = timedelta(seconds=1)
FILL_SN, FILL_PN = "xxx", "yyy"
# ------------------------------------------------


def _build_timeline() -> pd.DataFrame:
    df = pd.read_excel(INPUT_FILE, sheet_name=SHEET_NAME, dtype=str)
    df[TIME_COL] = pd.to_datetime(df[TIME_COL])
    df["_ord"] = df[FLAG_COL].map({"拆": 0, "装": 1})
    df = df.sort_values([PLANE_COL, TIME_COL, "_ord"]).reset_index(drop=True)

    sn2pn = df.drop_duplicates(SN_COL).set_index(SN_COL)[PART_COL].to_dict()

    timeline: list[dict] = []

    def add_snap(tl, plane, t, pos_sn, pos_pn, reason=""):
        tl.append(
            {
                PLANE_COL: plane,
                "时间": t,
                "idg1件号": pos_pn[0] or "",
                "idg1": pos_sn[0] or "",
                "idg2件号": pos_pn[1] or "",
                "idg2": pos_sn[1] or "",
                "原因": reason,
            }
        )

    def back_fill(tl, idx, sn, pn, plane):
        sn_key = f"idg{idx+1}"
        for row in reversed(tl):
            if row[PLANE_COL] != plane:
                break
            if row[sn_key] in ("", None):
                row[sn_key] = sn
                row[f"idg{idx+1}件号"] = pn
                return True
        return False

    def next_remove(sn, future):
        nxt = future[(future[FLAG_COL] == "拆") & (future[SN_COL] == sn)]
        return None if nxt.empty else nxt.iloc[0][TIME_COL]

    def choose_kick(pos_sn, future):
        s1, s2 = pos_sn
        t1, t2 = next_remove(s1, future), next_remove(s2, future)
        if t1 is None:
            return 0, s1
        if t2 is None:
            return 1, s2
        return (0, s1) if t1 > t2 else (1, s2)

    # -------- 主循环 --------
    for plane, grp in df.groupby(PLANE_COL):
        events = grp.reset_index(drop=True)

        # 推断初始 IDG
        init = []
        for _, r in events.iterrows():
            if r[FLAG_COL] == "拆":
                sn = str(r[SN_COL])
                if sn not in init:
                    init.append(sn)
                if len(init) == 2:
                    break
            else:
                break

        pos_sn, pos_pn = [None, None], [None, None]
        first_t = events.iloc[0][TIME_COL]
        for idx, sn in enumerate(init[:2]):
            pos_sn[idx] = sn
            pos_pn[idx] = sn2pn.get(sn, "")
            add_snap(
                timeline,
                plane,
                first_t - DELTA * (2 - idx),
                pos_sn,
                pos_pn,
                f"首记录前补装 {sn}",
            )

        for i, r in events.iterrows():
            dt, flag = r[TIME_COL], r[FLAG_COL]
            sn = str(r[SN_COL])
            pn = sn2pn.get(sn, "")
            future = events.iloc[i + 1 :]
            reason = ""

            if flag == "拆":
                if sn in pos_sn:
                    idx = pos_sn.index(sn)
                    pos_sn[idx] = pos_pn[idx] = None
                    reason = f"拆 {sn}"
                else:
                    if None in pos_sn:
                        idx = 1 if pos_sn[0] is None and pos_sn[1] is None else pos_sn.index(None)
                        if not back_fill(timeline, idx, sn, pn, plane):
                            add_snap(timeline, plane, dt - DELTA, [sn if idx == 0 else None, sn if idx == 1 else None],
                                     [pn if idx == 0 else None, pn if idx == 1 else None],
                                     f"补装 {sn}")
                        reason = f"拆 {sn}"
                    else:
                        kick_idx, kicked = choose_kick(pos_sn, future)
                        pos_sn[kick_idx], pos_pn[kick_idx] = sn, pn
                        add_snap(timeline, plane, dt - DELTA, pos_sn, pos_pn,
                                 f"补拆 {kicked} 并补装 {sn}")
                        pos_sn[kick_idx] = pos_pn[kick_idx] = None
                        reason = f"拆 {sn}"
            else:  # 装
                if sn not in pos_sn:
                    if None in pos_sn:
                        idx = pos_sn.index(None)
                        pos_sn[idx], pos_pn[idx] = sn, pn
                        reason = f"装 {sn}"
                    else:
                        kick_idx, kicked = choose_kick(pos_sn, future)
                        pos_sn[kick_idx], pos_pn[kick_idx] = sn, pn
                        add_snap(timeline, plane, dt - DELTA, pos_sn, pos_pn,
                                 f"补拆 {kicked} 并补装 {sn}")

            add_snap(timeline, plane, dt, pos_sn, pos_pn, reason)

    tl_df = pd.DataFrame(timeline)
    tl_df["日期"] = tl_df["时间"].dt.normalize()
    tl_df["is_patch"] = tl_df["原因"].str.startswith("补")

    patch_df = tl_df[tl_df["is_patch"]]
    daily = (
        tl_df[~tl_df["is_patch"]]
        .groupby(["机号", "日期"])
        .agg(
            {
                "时间": "last",
                "idg1件号": "last",
                "idg1": "last",
                "idg2件号": "last",
                "idg2": "last",
                "原因": lambda x: "；".join([s for s in x if s]),
            }
        )
        .reset_index()
        .sort_values(["机号", "时间"])
    )

    res = (
        pd.concat(
            [patch_df[["机号", "时间", "idg1件号", "idg1", "idg2件号", "idg2", "原因"]], daily],
            ignore_index=True,
        )
        .sort_values(["机号", "时间"])
        .reset_index(drop=True)
    )

    for col in ("idg1", "idg2"):
        res[col] = res[col].replace("", FILL_SN).fillna(FILL_SN)
    for col in ("idg1件号", "idg2件号"):
        res[col] = res[col].replace("", FILL_PN).fillna(FILL_PN)

    res[["idg1变化标记", "idg2变化标记"]] = ""
    for plane, g in res.groupby("机号"):
        for sn_col, flag_col in (("idg1", "idg1变化标记"), ("idg2", "idg2变化标记")):
            prev = None
            for idx in g.index:
                cur = res.at[idx, sn_col]
                if cur != FILL_SN and cur != prev:
                    res.at[idx, flag_col] = "是"
                    prev = cur
    return res


def generate_idg_history() -> Path:
    result = _build_timeline()
    with pd.ExcelWriter(OUTPUT_FILE, engine="openpyxl") as w:
        result.to_excel(w, sheet_name="IDG履历", index=False)
    print(f"[IDG] IDG 履历已生成：{OUTPUT_FILE}")
    return OUTPUT_FILE


if __name__ == "__main__":
    generate_idg_history()