# -*- coding: utf-8 -*-
"""
6 Sheet → 3 Sheet 合并工具

① 飞机-idg-装 + 飞机-idg-拆        →  飞机-idg
② 飞机-发动机-装 + 飞机-发动机-拆  →  飞机-发动机
③ 发动机-装 + 发动机-拆            →  发动机
"""
from __future__ import annotations
import os
from pathlib import Path
import pandas as pd
from mu_air_fore.config.settings import DATA_DIR

INPUT_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_简化_6sheet去重版.xlsx"
OUTPUT_FILE = DATA_DIR / "IDG及发动机拆换_截至2025_简化_3sheet.xlsx"
_GROUPS = [
    ("飞机-idg-装",    "飞机-idg-拆",    "飞机-idg"),
    ("飞机-发动机-装", "飞机-发动机-拆", "飞机-发动机"),
    ("发动机-装",      "发动机-拆",      "发动机"),
]

def _guess_cols(df: pd.DataFrame)->tuple[str, str]:
    id_col = "机号" if "机号" in df.columns else "物料"
    for col in ("拆换日期", "拆换时间", "时间"):
        if col in df.columns:
            return id_col, col
    raise ValueError("未找到日期列")

def _unify(df: pd.DataFrame)->None:
    part_cols = [c for c in ("件号","安装件号","拆下件号") if c in df.columns]
    if part_cols:
        df["件号"] = df[part_cols].bfill(axis=1).iloc[:,0]
    sn_cols = [c for c in ("序号","安装序号","拆下序号") if c in df.columns]
    if sn_cols:
        df["序号"] = df[sn_cols].bfill(axis=1).iloc[:,0]
    df.drop(columns=[c for c in ("安装件号","拆下件号","安装序号","拆下序号") if c in df.columns],
            inplace=True)

def merge_six_to_three()->Path:
    if not INPUT_FILE.exists():
        raise FileNotFoundError(f"缺少输入文件: {INPUT_FILE}")
    sheets = pd.read_excel(INPUT_FILE, sheet_name=None, dtype=str)
    OUTPUT_FILE.parent.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(OUTPUT_FILE, engine="openpyxl") as writer:
        for on, off, out in _GROUPS:
            df_on, df_off = sheets[on].copy(), sheets[off].copy()
            df_on["拆装标记"], df_off["拆装标记"] = "装", "拆"
            merged = pd.concat([df_off, df_on], ignore_index=True)
            _unify(merged)
            key, dt = _guess_cols(merged)
            merged[dt] = pd.to_datetime(merged[dt], errors="coerce")
            merged["拆装标记"] = pd.Categorical(merged["拆装标记"], ["拆","装"], ordered=True)
            merged.sort_values([key, dt, "拆装标记"], inplace=True, ignore_index=True)
            drop = ["拆下记录异常"] + (["机号"] if out=="发动机" else [])
            merged.drop(columns=[c for c in drop if c in merged.columns], inplace=True)
            merged.to_excel(writer, sheet_name=out, index=False)
    print(f"[IDG] 6→3 Sheet 合并完成：{os.path.abspath(OUTPUT_FILE)}")
    return OUTPUT_FILE

if __name__ == "__main__":
    merge_six_to_three()