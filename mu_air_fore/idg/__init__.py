# -*- coding: utf-8 -*-
"""
IDG / 发动机拆换数据处理子包
暴露主函数 `run_idg_flow`
"""
from .idg_data_pre import run_idg_flow
from .merge_sheets import merge_six_to_three
from .engine_history  import generate_engine_history
from .supplement_idg     import supplement_idg_parts
from .patch_idg_to_3sheet import patch_idg_to_3sheet
from .idg_history import generate_idg_history
from .outbound_records_pre import preprocess_outbound
from .link_outbound import link_idg_outbound

__all__ = [
    "run_idg_flow",
    "merge_six_to_three",
    "generate_engine_history",
    "supplement_idg_parts",
    "patch_idg_to_3sheet",
    "generate_idg_history",
    "preprocess_outbound",
    "link_idg_outbound",
]