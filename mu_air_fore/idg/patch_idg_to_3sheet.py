# -*- coding: utf-8 -*-
"""
把发动机履历中的 IDG 装记录回写到 3-sheet 文件首张表
生成: data/IDG及发动机拆换_截至2025_简化_3sheet_补发动机IDG.xlsx
"""
from __future__ import annotations
import os
from pathlib import Path
import pandas as pd
from mu_air_fore.config.settings import DATA_DIR

ENG_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_ENG履历_补IDG.xlsx"
SRC_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_简化_3sheet.xlsx"
OUT_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_简化_3sheet_补发动机IDG.xlsx"

ENG_SHEET = "发动机履历"
PLACEHOLDER_SN = "xxx"

def patch_idg_to_3sheet() -> Path:
    sheets = pd.read_excel(SRC_FILE, sheet_name=None, dtype=str)
    idg_sheet_name = list(sheets.keys())[0]          # 默认首张表
    idg_df = sheets[idg_sheet_name].copy()

    eng_df = pd.read_excel(ENG_FILE, sheet_name=ENG_SHEET, dtype=str)
    eng_df["时间"]       = pd.to_datetime(eng_df["时间"])
    idg_df["拆换日期"] = pd.to_datetime(idg_df["拆换日期"], errors="coerce")

    rows = []
    for _, r in eng_df.iterrows():
        for idx in (1, 2):
            idg_part = r.get(f"idg{idx}件号", "")
            idg_sn   = r.get(f"idg{idx}序号", "")
            eng_part = r.get(f"eng{idx}件号", "")
            eng_sn   = r.get(f"eng{idx}", "")

            if pd.notna(idg_sn) and str(idg_sn).strip().lower() != PLACEHOLDER_SN:
                rows.append(
                    {
                        "机号": r["机号"],
                        "拆换日期": r["时间"],
                        "拆装标记": "装",
                        "件号": idg_part,
                        "序号": idg_sn,
                        "物料": eng_part,
                        "序列号": eng_sn,
                        "发动机补充": "是",
                    }
                )

    supp_df = pd.DataFrame(rows)
    if supp_df.empty:
        print("[IDG] 没有需要补充的记录")
        return SRC_FILE

    key_cols = ["机号", "拆换日期", "拆装标记", "件号", "序号"]
    make_key = lambda d: d[key_cols].astype(str).agg("|".join, axis=1)
    supp_df = supp_df[~make_key(supp_df).isin(set(make_key(idg_df)))]

    if supp_df.empty:
        print("[IDG] 补充记录已全部存在，跳过")
        return SRC_FILE

    for col in ("物料", "序列号", "发动机补充"):
        if col not in idg_df.columns:
            idg_df[col] = ""

    idg_new = (
        pd.concat([idg_df, supp_df], ignore_index=True)
        .sort_values(["机号", "拆换日期", "拆装标记"])
    )
    sheets[idg_sheet_name] = idg_new

    OUT_FILE.parent.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(OUT_FILE, engine="openpyxl") as w:
        for name, df in sheets.items():
            df.to_excel(w, sheet_name=name, index=False)

    print(f"[IDG] 补发动机 IDG 完成：{os.path.abspath(OUT_FILE)}")
    return OUT_FILE


if __name__ == "__main__":
    patch_idg_to_3sheet()