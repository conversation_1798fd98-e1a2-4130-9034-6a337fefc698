# -*- coding: utf-8 -*-
"""
在发动机履历中补入 idg1 / idg2 件号与序号
生成：data/IDG及发动机拆换_截至2025_ENG履历_补IDG.xlsx
"""
from __future__ import annotations

import os
from pathlib import Path

import pandas as pd

from mu_air_fore.config.settings import DATA_DIR

# ---------- 参数 ----------
ENG_TL_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_ENG履历.xlsx"
SRC_FILE     = DATA_DIR / "IDG及发动机拆换_截至2025_简化_3sheet.xlsx"
OUT_FILE     = DATA_DIR / "IDG及发动机拆换_截至2025_ENG履历_补IDG.xlsx"

TL_SHEET   = "发动机履历"
SRC_SHEET  = "发动机"

PLACEHOLDER_SN = "xxx"
MAX_AGE_DAYS   = 1460      # 4 年
# --------------------------


def _prepare_sources():
    tl = pd.read_excel(ENG_TL_FILE,  sheet_name=TL_SHEET,  dtype=str)
    sc = pd.read_excel(SRC_FILE, sheet_name=SRC_SHEET, dtype=str)

    tl["时间"]      = pd.to_datetime(tl["时间"])
    sc["拆换日期"] = pd.to_datetime(sc["拆换日期"], errors="coerce")

    for c in ("eng1", "eng2"):
        tl[c] = tl[c].astype(str).str.strip()
    sc = sc.applymap(lambda x: x.strip() if isinstance(x, str) else x)

    sc = sc[["序列号", "件号", "序号", "拆换日期", "拆装标记"]].copy()
    sc.rename(columns={"序列号": "匹配键"}, inplace=True)
    sc.sort_values(["匹配键", "拆换日期"], inplace=True)
    return tl, sc


def _lookup(sc: pd.DataFrame, sn: str, ts) -> tuple[str | None, str | None, pd.Timestamp | None]:
    df = sc[sc["匹配键"] == sn]
    if df.empty:
        return None, None, None
    installs = df[(df["拆装标记"] == "装") & (df["拆换日期"] <= ts)]
    if installs.empty:
        return None, None, None

    for _, inst in installs.sort_values("拆换日期", ascending=False).iterrows():
        start = inst["拆换日期"]
        has_remove = (
            (df["拆装标记"] == "拆")
            & (df["拆换日期"] > start)
            & (df["拆换日期"] <= ts)
        ).any()
        if not has_remove:
            return inst["件号"], inst["序号"], start
    return None, None, None


def supplement_idg_parts() -> Path:
    tl, sc = _prepare_sources()

    for col in ("idg1件号", "idg1序号", "idg2件号", "idg2序号"):
        tl[col] = ""

    for idx, row in tl.iterrows():
        ts = row["时间"]

        for eng_idx, (sn_col, idg_part_col, idg_sn_col) in enumerate(
            (
                ("eng1", "idg1件号", "idg1序号"),
                ("eng2", "idg2件号", "idg2序号"),
            ),
            start=1,
        ):
            sn = row[sn_col]
            if sn and sn != PLACEHOLDER_SN:
                part, seq, start = _lookup(sc, sn, ts)
                if part is not None and (ts - start).days <= MAX_AGE_DAYS:
                    tl.at[idx, idg_part_col] = part
                    tl.at[idx, idg_sn_col]   = seq

    tl.drop(columns=["eng1变化标记", "eng2变化标记", "原因"], errors="ignore", inplace=True)

    order = [
        "机号", "时间",
        "eng1件号", "eng1", "idg1件号", "idg1序号",
        "eng2", "eng2件号", "idg2件号", "idg2序号",
    ]
    tl = tl[[c for c in order if c in tl.columns] + [c for c in tl.columns if c not in order]]

    with pd.ExcelWriter(OUT_FILE, engine="openpyxl") as w:
        tl.to_excel(w, sheet_name="发动机履历", index=False)
    print(f"[IDG] 发动机履历已补入 IDG：{os.path.abspath(OUT_FILE)}")
    return OUT_FILE


if __name__ == "__main__":
    supplement_idg_parts()