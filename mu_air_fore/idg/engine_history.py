# -*- coding: utf-8 -*-
"""
根据 3-sheet 文件中“飞机-发动机”Sheet，推演完整发动机拆装履历。
生成文件：data/IDG及发动机拆换_截至2025_ENG履历.xlsx
"""
from __future__ import annotations

import os
from datetime import timedelta
from pathlib import Path

import pandas as pd

from mu_air_fore.config.settings import DATA_DIR

# ---------------- 参数 ----------------
INPUT_FILE  = DATA_DIR / "IDG及发动机拆换_截至2025_简化_3sheet.xlsx"
OUTPUT_FILE = DATA_DIR / "IDG及发动机拆换_截至2025_ENG履历.xlsx"
SHEET_NAME  = "飞机-发动机"

TIME_COL, FLAG_COL = "拆换日期", "拆装标记"
SN_COL,  PART_COL  = "序号", "件号"
PLANE_COL          = "机号"

DELTA = timedelta(seconds=1)
FILL_SN, FILL_PN = "xxx", "yyy"
# --------------------------------------


def _build_timeline() -> pd.DataFrame:
    df = pd.read_excel(INPUT_FILE, sheet_name=SHEET_NAME, dtype=str)
    df[TIME_COL] = pd.to_datetime(df[TIME_COL])
    df["_ord"] = df[FLAG_COL].map({"拆": 0, "装": 1})
    df = df.sort_values([PLANE_COL, TIME_COL, "_ord"]).reset_index(drop=True)

    sn2pn = df.drop_duplicates(SN_COL).set_index(SN_COL)[PART_COL].to_dict()

    tl: list[dict] = []

    def add_snap(plane, dt, pos_sn, pos_pn, reason=""):
        tl.append(
            {
                PLANE_COL: plane,
                "时间": dt,
                "eng1件号": pos_pn[0] or "",
                "eng1": pos_sn[0] or "",
                "eng2件号": pos_pn[1] or "",
                "eng2": pos_sn[1] or "",
                "原因": reason,
            }
        )

    def back_fill(tline, idx, serial, partnum, plane):
        sn_key = f"eng{idx+1}"
        for row in reversed(tline):
            if row[PLANE_COL] != plane:
                break
            if row[sn_key] in ("", None):
                row[sn_key] = serial
                row[f"eng{idx+1}件号"] = partnum
                return True
        return False

    def next_remove(sn, future):
        nxt = future[(future[FLAG_COL] == "拆") & (future[SN_COL] == sn)]
        return None if nxt.empty else nxt.iloc[0][TIME_COL]

    def choose_kick(pos_sn, future):
        s1, s2 = pos_sn
        t1, t2 = next_remove(s1, future), next_remove(s2, future)
        if t1 is None:
            return 0, s1
        if t2 is None:
            return 1, s2
        return (0, s1) if t1 > t2 else (1, s2)

    for plane, grp in df.groupby(PLANE_COL):
        events = grp.reset_index(drop=True)

        # ---- 推断初始两台发动机 ----
        init = []
        for _, r in events.iterrows():
            if r[FLAG_COL] == "拆":
                sn = str(r[SN_COL])
                if sn not in init:
                    init.append(sn)
                if len(init) == 2:
                    break
            else:
                break

        pos_sn, pos_pn = [None, None], [None, None]
        first_dt = events.iloc[0][TIME_COL]
        for idx, sn in enumerate(init[:2]):
            pos_sn[idx] = sn
            pos_pn[idx] = sn2pn.get(sn, "")
            add_snap(plane, first_dt - DELTA * (2 - idx), pos_sn, pos_pn, f"首记录前补装 {sn}")

        # ---- 事件循环 ----
        for i, r in events.iterrows():
            dt, flag = r[TIME_COL], r[FLAG_COL]
            sn = str(r[SN_COL])
            pn = sn2pn.get(sn, "")
            future = events.iloc[i + 1 :]
            reason = ""

            if flag == "拆":
                if sn in pos_sn:
                    idx = pos_sn.index(sn)
                    pos_sn[idx] = pos_pn[idx] = None
                    reason = f"拆 {sn}"
                else:
                    if None in pos_sn:
                        idx = pos_sn.index(None)
                        filled = back_fill(tl, idx, sn, pn, plane)
                        if not filled:
                            pos_sn[idx], pos_pn[idx] = sn, pn
                            add_snap(plane, dt - DELTA, pos_sn, pos_pn, f"补装 {sn}")
                            pos_sn[idx] = pos_pn[idx] = None
                        reason = f"拆 {sn}"
                    else:
                        kick_idx, kicked = choose_kick(pos_sn, future)
                        pos_sn[kick_idx], pos_pn[kick_idx] = sn, pn
                        add_snap(plane, dt - DELTA, pos_sn, pos_pn, f"补拆 {kicked} 并补装 {sn}")
                        pos_sn[kick_idx] = pos_pn[kick_idx] = None
                        reason = f"拆 {sn}"
            else:  # 装
                if sn not in pos_sn:
                    if None in pos_sn:
                        idx = pos_sn.index(None)
                        pos_sn[idx], pos_pn[idx] = sn, pn
                        reason = f"装 {sn}"
                    else:
                        kick_idx, kicked = choose_kick(pos_sn, future)
                        pos_sn[kick_idx], pos_pn[kick_idx] = sn, pn
                        add_snap(plane, dt - DELTA, pos_sn, pos_pn, f"补拆 {kicked} 并补装 {sn}")

            add_snap(plane, dt, pos_sn, pos_pn, reason)

    tl_df = pd.DataFrame(tl)
    tl_df["日期"] = tl_df["时间"].dt.normalize()
    tl_df["is_patch"] = tl_df["原因"].str.startswith("补")

    patch_df = tl_df[tl_df["is_patch"]]
    daily = (
        tl_df[~tl_df["is_patch"]]
        .groupby([PLANE_COL, "日期"])
        .agg(
            {
                "时间": "last",
                "eng1件号": "last",
                "eng1": "last",
                "eng2件号": "last",
                "eng2": "last",
                "原因": lambda x: "；".join([s for s in x if s]),
            }
        )
        .reset_index()
        .sort_values([PLANE_COL, "时间"])
    )

    res = (
        pd.concat(
            [patch_df[[PLANE_COL, "时间", "eng1件号", "eng1", "eng2件号", "eng2", "原因"]], daily],
            ignore_index=True,
        )
        .sort_values([PLANE_COL, "时间"])
        .reset_index(drop=True)
    )

    # 填占位
    for col in ("eng1", "eng2"):
        res[col] = res[col].replace("", FILL_SN).fillna(FILL_SN)
    for col in ("eng1件号", "eng2件号"):
        res[col] = res[col].replace("", FILL_PN).fillna(FILL_PN)

    # 变化标记
    res[["eng1变化标记", "eng2变化标记"]] = ""
    for plane, g in res.groupby(PLANE_COL):
        for col_sn, flag_col in (("eng1", "eng1变化标记"), ("eng2", "eng2变化标记")):
            prev = None
            for idx in g.index:
                cur = res.at[idx, col_sn]
                if cur != FILL_SN and cur != prev:
                    res.at[idx, flag_col] = "是"
                    prev = cur
    return res


def generate_engine_history() -> Path:
    """对外主函数"""
    result = _build_timeline()
    with pd.ExcelWriter(OUTPUT_FILE, engine="openpyxl") as w:
        result.to_excel(w, sheet_name="发动机履历", index=False)
    print(f"[IDG] 发动机履历生成完毕：{os.path.abspath(OUTPUT_FILE)}")
    return OUTPUT_FILE


if __name__ == "__main__":
    generate_engine_history()