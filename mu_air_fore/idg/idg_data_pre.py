# -*- coding: utf-8 -*-
"""
整合 IDG 三段脚本的完整流程
生成三个输出：
1) _补充设备信息.xlsx
2) _补充设备信息_去重版2.xlsx
3) _简化_6sheet去重版.xlsx
"""
from __future__ import annotations

from pathlib import Path
from typing import Set

import pandas as pd

from mu_air_fore.config.settings import DATA_DIR


# --------------------------- 参数 --------------------------- #
MAIN_FILE = DATA_DIR / "IDG及发动机拆换_截至2025.xlsx"
IDG_FILE = DATA_DIR / "idg拆换记录.XLSX"

OUT_FILE_1 = DATA_DIR / "IDG及发动机拆换_截至2025_补充设备信息.xlsx"
OUT_FILE_2 = DATA_DIR / "IDG及发动机拆换_截至2025_补充设备信息_去重版.xlsx"
OUT_FILE_3 = DATA_DIR / "IDG及发动机拆换_截至2025_简化_6sheet去重版.xlsx"

ENGINE_TYPES: Set[str] = {
    "CFM56-5B", "CFM56-7B", "TRENT772B-60", "CFM56-5B3/P",
    "TRENT772C-60", "LEAP-1B",
}


# ---------------------- 工具函数 ---------------------- #
def _clean_code(x):
    x = str(x).strip()
    if x.endswith(".0"):
        x = x[:-2]
    return x


def _supplement_device_info() -> None:
    """阶段 1：补全设备、物料、序列号"""
    main_df = pd.read_excel(MAIN_FILE, sheet_name="Sheet1")

    idg_record_df = pd.read_excel(IDG_FILE, sheet_name="拆换记录")
    idg_device_df = pd.read_excel(IDG_FILE, sheet_name="高级设备发动机信息")

    # 统一格式
    for col in ["记录纸编号", "记录本编号", "记录纸序号"]:
        main_df[col] = main_df[col].apply(_clean_code)
        idg_record_df[col] = idg_record_df[col].apply(_clean_code)

    main_df["高级设备"] = (
        main_df.merge(
            idg_record_df[["记录纸编号", "记录本编号", "记录纸序号", "高级设备"]],
            on=["记录纸编号", "记录本编号", "记录纸序号"],
            how="left",
        )["高级设备"]
        .apply(_clean_code)
    )

    idg_device_df["设备"] = idg_device_df["设备"].apply(_clean_code)

    merged = main_df.merge(
        idg_device_df[["设备", "物料", "序列号"]],
        left_on="高级设备",
        right_on="设备",
        how="left",
    )

    output_cols = list(main_df.columns) + ["高级设备", "设备", "物料", "序列号"]
    merged[output_cols].to_excel(OUT_FILE_1, index=False)
    print(f"[IDG] 已生成补充文件：{OUT_FILE_1.name}")


def _deduplicate() -> None:
    """阶段 2：全字段去重"""
    df = pd.read_excel(OUT_FILE_1)
    df_dedup = df.drop_duplicates(
        ["机号", "拆换日期", "安装序号", "安装件号", "拆下件号", "拆下序号"]
    )
    df_dedup.to_excel(OUT_FILE_2, index=False)
    print(f"[IDG] 去重完成：{OUT_FILE_2.name}")


def _split_to_sheets() -> None:
    """阶段 3：分类生成 6 个 Sheet"""
    df = pd.read_excel(OUT_FILE_2)

    # 判断类型 engine / idg
    def _get_type(row):
        return "engine" if (str(row["安装件号"]) in ENGINE_TYPES or str(row["拆下件号"]) in ENGINE_TYPES) else "idg"

    df["type"] = df.apply(_get_type, axis=1)

    exclude_cols = ["记录纸编号", "记录本编号", "记录纸序号", "物料", "序列号"]

    def _drop_dup(sub_df):
        return sub_df.drop_duplicates(subset=[c for c in sub_df.columns if c not in exclude_cols])

    # --- 6 个子表 ---
    sheet1 = _drop_dup(
        df[(df["type"] == "idg") & df["机号"].notna() & df["安装序号"].notna() & df["安装件号"].notna()][
            ["机号", "记录纸编号", "记录本编号", "记录纸序号", "拆换日期", "安装序号", "安装件号", "物料", "序列号"]
        ]
    )

    sheet2 = _drop_dup(
        df[(df["type"] == "idg") & df["机号"].notna() & df["拆下件号"].notna() & df["拆下序号"].notna()][
            ["机号", "记录纸编号", "记录本编号", "记录纸序号", "拆换日期", "拆下序号", "拆下件号", "物料", "序列号"]
        ]
    )

    sheet3 = _drop_dup(
        df[(df["type"] == "engine") & df["机号"].notna() & df["安装序号"].notna() & df["安装件号"].notna()][
            ["机号", "记录纸编号", "记录本编号", "记录纸序号", "拆换日期", "安装序号", "安装件号", "物料", "序列号"]
        ]
    )

    sheet4 = _drop_dup(
        df[(df["type"] == "engine") & df["机号"].notna() & df["拆下件号"].notna() & df["拆下序号"].notna()][
            ["机号", "记录纸编号", "记录本编号", "记录纸序号", "拆换日期", "拆下序号", "拆下件号", "物料", "序列号"]
        ]
    )

    sheet5 = _drop_dup(
        df[df["物料"].notna() & df["序列号"].notna() & df["安装件号"].notna() & df["安装序号"].notna()][
            ["物料", "序列号", "记录纸编号", "记录本编号", "记录纸序号", "拆换日期", "安装序号", "安装件号", "机号"]
        ]
    )

    sheet6 = _drop_dup(
        df[df["物料"].notna() & df["序列号"].notna() & df["拆下件号"].notna() & df["拆下序号"].notna()][
            ["物料", "序列号", "记录纸编号", "记录本编号", "记录纸序号", "拆换日期", "拆下序号", "拆下件号", "机号"]
        ]
    )

    with pd.ExcelWriter(OUT_FILE_3) as writer:
        sheet1.to_excel(writer, sheet_name="飞机-idg-装", index=False)
        sheet2.to_excel(writer, sheet_name="飞机-idg-拆", index=False)
        sheet3.to_excel(writer, sheet_name="飞机-发动机-装", index=False)
        sheet4.to_excel(writer, sheet_name="飞机-发动机-拆", index=False)
        sheet5.to_excel(writer, sheet_name="发动机-装", index=False)
        sheet6.to_excel(writer, sheet_name="发动机-拆", index=False)

    print(f"[IDG] 6 Sheet 文件已生成：{OUT_FILE_3.name}")


# ---------------------- 主流程 ---------------------- #
def run_idg_flow() -> None:
    """
    对外统一入口
    """
    _supplement_device_info()
    _deduplicate()
    _split_to_sheets()
    print("[IDG] 整个 IDG 拆换流程完成")