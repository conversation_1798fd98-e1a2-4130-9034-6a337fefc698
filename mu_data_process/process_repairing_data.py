import pandas as pd
from sqlalchemy import create_engine, text
from mu_air_fore.config.db_settings import SQLALCHEMY_URL


def process_repairing_data(repairing_data, storage_data, reference_date='2025-05-08'):
    """
    处理在修零部件数据并生成指定格式的Excel文件

    参数:
        input_excel_path(pd.DataFrame) : 送修合同清单的DataFrame
        storage_data (pd.DataFrame): 包含库存地代码和库存地的DataFrame
        reference_date (str): 计算已修理时长的参考日期，默认'2025-05-08'
    """

    # 筛选送修合同状态为OPEN的数据
    repairing_data = repairing_data[repairing_data['状态'] == 'OPEN'].reset_index(drop=True)

    # 计算已修理时长(天)
    repairing_data['已修理时长(天)'] = (pd.Timestamp(reference_date) - repairing_data['出库日期']).dt.days

    # 合并库存地信息
    selected_features = ['件号', '序号', '出库日期', '已修理时长(天)', '合同库存地']
    re_df = pd.merge(
        repairing_data[selected_features],
        storage_data[['库存地代码', '库存地']],
        left_on='合同库存地',
        right_on='库存地代码',
        how='left'
    )

    # 处理地点列
    def clean_location(location):
        chars_to_remove = ['验收库', '子公司', '公司']
        for char in chars_to_remove:
            location = location.replace(char, '')
        return location

    re_df['地点'] = re_df['库存地'].apply(clean_location)

    # 选择最终需要的列并输出
    final_features = ['件号', '序号', '出库日期', '已修理时长(天)', '合同库存地', '地点']
    result_df = re_df[final_features]

    return result_df


def save_repairing_data_to_mysql(result_df: pd.DataFrame) -> None:
    """
    将在修零部件数据写入 MySQL pn_back_repair_prediction 表

    参数:
        result_df: 包含在修零部件数据的 DataFrame
    """
    # 创建数据库连接
    engine = create_engine(SQLALCHEMY_URL, echo=False)

    # 复制数据框以避免修改原始数据
    df_to_save = result_df.copy()

    # 字段映射 - 将中文列名映射到数据库字段名
    column_mapping = {
        '件号': 'pnr',
        '序号': 'seq',
        '出库日期': 'ckrq',
        '已修理时长(天)': 'yxlsc',
        '合同库存地': 'htkcd',
        '地点': 'dd'
    }

    # 重命名列
    df_to_save.rename(columns=column_mapping, inplace=True)

    # 选择需要保存到数据库的列
    columns_to_save = list(column_mapping.values())
    # 确保只选择存在的列
    columns_to_save = [col for col in columns_to_save if col in df_to_save.columns]
    df_to_save = df_to_save[columns_to_save]

    # 先清空表中的所有数据
    with engine.begin() as conn:
        conn.execute(text("TRUNCATE TABLE `pn_back_repair_prediction`"))

    # 写入数据库
    df_to_save.to_sql(
        name='pn_back_repair_prediction',
        con=engine,
        if_exists='append',
        index=False,
        chunksize=2000,
        method='multi'
    )

    # 输出反馈信息
    with engine.begin() as conn:
        total = conn.execute(text("SELECT COUNT(*) FROM `pn_back_repair_prediction`")).scalar()
    print(f"[DB] pn_back_repair_prediction 导入完成，新增 {len(df_to_save)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    # 读取数据
#     rp_data = pd.read_excel('data/数据.xlsx', sheet_name='送修合同清单')
    rp_data = pd.read_excel('/home/<USER>/mu_air_python/mu_air_python/data/送修合同清单.xlsx')
#     storage_data = pd.read_excel('data/库存地类别.xlsx')
    storage_data = pd.read_excel('/home/<USER>/mu_air_python/mu_air_python/data/库存地类别.xlsx')

    # 处理数据
    result_df = process_repairing_data(rp_data, storage_data)

    # 保存为Excel文件
    output_file = "data/在修零部件数据.xlsx"
    result_df.to_excel(output_file, index=False, sheet_name='在修零部件')

    # 保存到MySQL数据库
    save_repairing_data_to_mysql(result_df)

    print(f"在修零部件数据已保存到: {output_file}")
    print(f"共处理 {len(result_df)} 条在修记录")