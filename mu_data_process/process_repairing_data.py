import pandas as pd


def process_repairing_data(repairing_data, storage_data, reference_date='2025-05-08'):
    """
    处理在修零部件数据并生成指定格式的Excel文件

    参数:
        input_excel_path(pd.DataFrame) : 送修合同清单的DataFrame
        storage_data (pd.DataFrame): 包含库存地代码和库存地的DataFrame
        reference_date (str): 计算已修理时长的参考日期，默认'2025-05-08'
    """

    # 筛选送修合同状态为OPEN的数据
    repairing_data = repairing_data[repairing_data['状态'] == 'OPEN'].reset_index(drop=True)

    # 计算已修理时长(天)
    repairing_data['已修理时长(天)'] = (pd.Timestamp(reference_date) - repairing_data['出库日期']).dt.days

    # 合并库存地信息
    selected_features = ['件号', '序号', '出库日期', '已修理时长(天)', '合同库存地']
    re_df = pd.merge(
        repairing_data[selected_features],
        storage_data[['库存地代码', '库存地']],
        left_on='合同库存地',
        right_on='库存地代码',
        how='left'
    )

    # 处理地点列
    def clean_location(location):
        chars_to_remove = ['验收库', '子公司', '公司']
        for char in chars_to_remove:
            location = location.replace(char, '')
        return location

    re_df['地点'] = re_df['库存地'].apply(clean_location)

    # 选择最终需要的列并输出
    final_features = ['件号', '序号', '出库日期', '已修理时长(天)', '合同库存地', '地点']
    result_df = re_df[final_features]

    return result_df


if __name__ == "__main__":
    # 读取数据
    rp_data = pd.read_excel('data/数据.xlsx', sheet_name='送修合同清单')
    storage_data = pd.read_excel('data/库存地类别.xlsx')

    # 处理数据
    result_df = process_repairing_data(rp_data, storage_data)

    # 保存为Excel文件
    output_file = "data/在修零部件数据.xlsx"
    result_df.to_excel(output_file, index=False, sheet_name='在修零部件')

    print(f"在修零部件数据已保存到: {output_file}")
    print(f"共处理 {len(result_df)} 条在修记录")