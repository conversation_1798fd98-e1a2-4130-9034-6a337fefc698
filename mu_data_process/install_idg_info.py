import pandas as pd
def install_idg_info(idg_rec, store_data, storage, airplane, flight_data, fleet, repair_data,
                    repair_record, whole_tsn, airport_mapping, fleet_mapping):
    # ----------------------
    # 1. 处理IDG装机记录数据
    # ----------------------
    # 读取IDG履历数据并获取最后一次装机记录
    idg_rec.drop_duplicates(subset='机号', keep='last', inplace=True)
    idg_rec.replace(['yyy', 'xxx'], None, inplace=True)
    idg_rec.reset_index(drop=True, inplace=True)

    # 拆分IDG1和IDG2数据并合并
    idg1 = idg_rec[['机号', '时间', 'idg1件号', 'idg1']].rename(
        columns={'idg1件号': 'idg件号', 'idg1': 'idg序号'}
    )
    idg2 = idg_rec[['机号', '时间', 'idg2件号', 'idg2']].rename(
        columns={'idg2件号': 'idg件号', 'idg2': 'idg序号'}
    )
    idg = pd.concat([idg1, idg2], axis=0)

    # 筛选有效记录并保留最新装机信息
    idg = (idg.dropna(subset=['idg件号', 'idg序号'])
           .sort_values('时间')
           .drop_duplicates(subset=['idg件号', 'idg序号'], keep='last')
           .reset_index(drop=True))

    # ----------------------
    # 2. 处理库存与匹配状态
    # ----------------------
    # 库存数据处理状态

    # 构建库存地信息字典
    store_dict = {
        row['库存地代码']: {
            '库存地': row['库存地'],
            '库存地类别': row['库存地类别']
        } for _, row in storage.iterrows()
    }

    # 处理库存状态
    def process_storage_status(df):
        df[['状态', '库存地', '库存地类别']] = None
        for idx, row in df.iterrows():
            if row['系统状态'] == 'ESTO':
                if pd.isna(row['特殊库存']):
                    status = '在厂家库存地' if row['库存地点'] == 'GYS1' else '在库'
                else:
                    status = '在修' if row['特殊库存'] == 'O' else '其他库存类型'
            elif row['系统状态'] == 'ASEQ':
                status = '安装'
            else:
                status = '其他库存类型'

            loc_info = store_dict.get(row['库存地点'], {})
            df.loc[idx, ['状态', '库存地', '库存地类别']] = [
                status, loc_info.get('库存地'), loc_info.get('库存地类别')
            ]
        return df

    processed_store_data = process_storage_status(store_data)

    # 提取安装状态的IDG信息
    store_install = set(
        zip(processed_store_data[processed_store_data['状态'] == '安装']['物料'],
            processed_store_data[processed_store_data['状态'] == '安装']['序列号'])
    )

    # 匹配IDG安装状态
    idg['匹配状态'] = idg.apply(
        lambda x: '可匹配' if (x['idg件号'], x['idg序号']) in store_install else '不可匹配', axis=1
    )

    # ----------------------
    # 3. 筛选在用飞机的有效IDG
    # ----------------------
    using_airplane = list(airplane[airplane['飞机状态'] == '在用']['机号'].unique())
    inst_idg_df = idg[idg['机号'].isin(using_airplane) & (idg['匹配状态'] == '可匹配')].reset_index(drop=True)

    # ----------------------
    # 4. 处理机场与基地信息
    # 分析过夜基地

    # 计算航班飞行时间和航后停场时间
    def calculate_flight_and_ground_time(flight_data):
        # 确保数据按机号和起飞时间排序
        flight_data = flight_data.dropna(subset=['机号']).reset_index(drop=True)
        flight_data = flight_data.sort_values(['机号', '计划起飞日期', '计划起飞时间'])

        # 创建计划起飞和到达的完整时间列
        flight_data['起飞完整时间'] = pd.to_datetime(
            flight_data['计划起飞日期'].astype(str) + ' ' + flight_data['计划起飞时间'].astype(str)
        )

        flight_data['到达完整时间'] = pd.to_datetime(
            flight_data['计划到达日期'].astype(str) + ' ' + flight_data['计划到达时间'].astype(str)
        )

        flight_data['航班飞行时间(小时)'] = (flight_data['到达完整时间'] - flight_data[
            '起飞完整时间']).dt.total_seconds() / 3600

        # 按机号分组并计算每个航班的下一次起飞时间
        flight_data['下次起飞时间'] = flight_data.groupby('机号')['起飞完整时间'].shift(-1)

        # 计算停场时间（下次起飞时间减去本次到达时间），并转换为小时
        flight_data['停场时间(小时)'] = (flight_data['下次起飞时间'] - flight_data[
            '到达完整时间']).dt.total_seconds() / 3600

        flight_data['停场时间(小时)'] = flight_data['停场时间(小时)'].fillna(0)

        return flight_data

    # 计算航班飞行时间和航后停场时间
    processed_flight_data = calculate_flight_and_ground_time(flight_data)

    # 计算每架飞机过夜次数最多的航站
    def find_most_frequent_overnight_stations(processed_flight_data, airport_mapping):

        # 创建日期列（基于到达时间）
        processed_flight_data['到达日期'] = processed_flight_data['到达完整时间'].dt.date

        # 过滤掉停场时间为NaN的行
        valid_flights = processed_flight_data.dropna(subset=['停场时间(小时)'])

        # 按机号和到达日期分组，找出每天最长停场时间的记录
        longest_ground_time = valid_flights.loc[
            valid_flights.groupby(['机号', '到达日期'])['停场时间(小时)'].idxmax()
        ][['机号', '到达日期', '到达航站', '停场时间(小时)']]

        # 重命名列
        longest_ground_time.columns = ['机号', '日期', '过夜航站', '停场时间(小时)']

        # 将过夜航站映射为location
        longest_ground_time['过夜航站'] = longest_ground_time['过夜航站'].apply(
            lambda x: airport_mapping.get(x, {}).get('location', x)
        )

        # 生成每个机号的最多过夜航站字典
        most_frequent_stations = longest_ground_time.groupby('机号')['过夜航站'].agg(
            lambda x: x.value_counts().index[0]
        ).to_dict()

        return most_frequent_stations

    overnight_base_dict = find_most_frequent_overnight_stations(processed_flight_data, airport_mapping)

    # 执管基地处理

    loc_dict = {i[1]: i[2] for i in fleet_mapping}
    fleet_location_dict = {row['机号']: loc_dict.get(row['指定机号单位']) for _, row in fleet.iterrows()}

    # 计算飞机的排班率和飞机的日均使用时长
    def airplane_flight_info(processed_flight_data):
        flight_schedule = processed_flight_data.groupby('机号').agg({
            '计划起飞日期': ['min', 'max', 'nunique'],
            '航班飞行时间(小时)': 'sum',
            '机号': 'count'
        }).reset_index()

        # 重命名列
        flight_schedule.columns = ['机号', '排班开始日期', '排班结束日期', '飞行天数', '飞行时长(小时)', '飞行循环']

        flight_schedule['排班天数'] = (flight_schedule['排班结束日期'] - flight_schedule['排班开始日期']).dt.days + 1
        flight_schedule['日均飞行时长'] = flight_schedule['飞行时长(小时)'] / flight_schedule['飞行天数']
        flight_schedule['日均飞行循环'] = flight_schedule['飞行循环'] / flight_schedule['飞行天数']
        flight_schedule['循环平均飞行时长'] = flight_schedule['飞行时长(小时)'] / flight_schedule['飞行循环']
        flight_schedule['排班率'] = flight_schedule['飞行天数'] / flight_schedule['排班天数']

        flight_schedule_dict = flight_schedule.set_index('机号').to_dict('index')

        return flight_schedule_dict

    flight_schedule_dict = airplane_flight_info(processed_flight_data)

    def process_repair_contracts(df):
        """
        处理送修合同数据，计算各项时长指标并按件号统计TAT数据

        参数:
        df (pd.DataFrame): 包含送修合同信息的DataFrame

        返回:
        pd.DataFrame: 按件号分组的TAT统计结果
        """
        # 只看已完成的送修合同
        df = df[df['状态'] == 'CLOSE'].reset_index(drop=True)

        def calculate_durations(df):
            # 确保日期列是datetime类型
            date_columns = ['入库日期', '拆下日期', '系统拆下日期', '合同日期', '出库日期', '签收日期', '检验决策日期']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col])

            # 生成整合退库日期列
            df['整合退库日期'] = df['退库日期'].fillna(df['系统拆下日期']).fillna(df['拆下日期'])

            # 计算各时长列
            if '合同日期' in df.columns and '整合退库日期' in df.columns:
                df['送修采购消耗时长'] = (df['合同日期'] - df['整合退库日期']).dt.days

            if '签收日期' in df.columns and '出库日期' in df.columns:
                df['修理时长'] = (df['签收日期'] - df['出库日期']).dt.days

            if '检验决策日期' in df.columns and '签收日期' in df.columns:
                df['入可用库消耗时长'] = (df['检验决策日期'] - df['签收日期']).dt.days

            df['维修总时长'] = df['送修采购消耗时长'] + df['修理时长'] + df['入可用库消耗时长']

            return df

        # 计算时长
        df = calculate_durations(df)

        filter_df = df[['件号', '序号', '出库日期', '修理时长']].dropna().reset_index(drop=True)

        def calculate_tat_statistics(df):
            # 确保修理时长列存在且为数值类型
            if '修理时长' not in df.columns:
                raise ValueError("DataFrame中缺少'修理时长'列")

            # 按件号分组并计算统计量
            tat_stats = df.groupby('件号').agg(
                累计维修次数=('修理时长', 'count'),
                平均TAT时长=('修理时长', 'mean'),
                中位数TAT时长=('修理时长', 'median'),
                TAT标准差=('修理时长', 'std'),
                最小TAT时长=('修理时长', 'min'),
                最大TAT时长=('修理时长', 'max'),
                送修TAT时长_20=('修理时长', lambda x: x.quantile(0.2)),
                送修TAT时长_40=('修理时长', lambda x: x.quantile(0.4)),
                送修TAT时长_60=('修理时长', lambda x: x.quantile(0.6)),
                送修TAT时长_80=('修理时长', lambda x: x.quantile(0.8))
            ).reset_index()

            # 重命名分位数列以符合要求
            tat_stats.rename(columns={
                '送修TAT时长_20': '20%的送修TAT时长',
                '送修TAT时长_40': '40%的送修TAT时长',
                '送修TAT时长_60': '60%的送修TAT时长',
                '送修TAT时长_80': '80%的送修TAT时长'
            }, inplace=True)

            # 保留两位小数
            tat_stats[['平均TAT时长', '中位数TAT时长', 'TAT标准差',
                       '最小TAT时长', '最大TAT时长', '20%的送修TAT时长',
                       '40%的送修TAT时长', '60%的送修TAT时长', '80%的送修TAT时长']] = tat_stats[
                ['平均TAT时长', '中位数TAT时长', 'TAT标准差',
                 '最小TAT时长', '最大TAT时长', '20%的送修TAT时长',
                 '40%的送修TAT时长', '60%的送修TAT时长', '80%的送修TAT时长']
            ].round(2)

            return tat_stats

        # 计算TAT统计数据
        tat_stats_df = calculate_tat_statistics(df)

        return tat_stats_df

    tat_stats_df = process_repair_contracts(repair_data)
    tat_dict = tat_stats_df.set_index('件号').to_dict('index')

    # ----------------------
    # 5. 处理使用时长数据
    # ----------------------

    repair_dict = repair_record.set_index('序号')['TSR'].to_dict()

    tsn_dict = whole_tsn.set_index('序列号')['TSN'].to_dict()

    # ----------------------
    # 6. 生成最终JSON数据
    # ----------------------
    def generate_json(df):
        result = {}
        for _, row in df.iterrows():
            idg_id = row['idg序号']
            # 获取TAT时长，默认为0
            tat_info = tat_dict.get(row['idg件号'], {})
            pnr_tat = int(tat_info.get('80%的送修TAT时长', 0))

            # 获取机号相关信息
            aircraft_info = flight_schedule_dict.get(row['机号'], {})
            duty_rate = aircraft_info.get('排班率', 0)
            daily_flight_hours = aircraft_info.get('日均飞行时长', 0)

            # 计算TAT周期内的飞行时长
            flight_hours_in_tat = round(pnr_tat * duty_rate * daily_flight_hours, 2)

            # 构建结果字典
            result[idg_id] = {
                '件号': row['idg件号'],
                '机号': row['机号'],
                '最后排班日期': aircraft_info.get('排班结束日期'),
                '排班结束前飞行时长': round(aircraft_info.get('飞行时长(小时)', 0), 2),
                '执管基地': fleet_location_dict.get(row['机号']),
                '过夜基地': overnight_base_dict.get(row['机号']),
                'TAT周期': pnr_tat,
                'TAT周期内的飞行时长': flight_hours_in_tat,
                '已使用时长': repair_dict.get(idg_id) or tsn_dict.get(idg_id)
            }
        return result

    serial_json = generate_json(inst_idg_df)
    return serial_json


if __name__ == "__main__":
    idg_rec = pd.read_excel('data/IDG及发动机拆换_截至2025_IDG履历.xlsx')
    # 当前需要把拆换履历定在8月7日及以前
    idg_rec = idg_rec[idg_rec['时间'] <= pd.to_datetime('2025-05-07 23:59:59')].reset_index(drop=True)
    store_data = pd.read_excel('data/数据.xlsx', sheet_name='设备清单及库存')
    storage = pd.read_excel('data/库存地类别.xlsx')
    airplane = pd.read_excel('data/数据.xlsx', sheet_name='机队数据')
    flight_data = pd.read_excel('data/数据.xlsx', sheet_name='航班202505-06')
    inspection_schedule = pd.read_excel('data/数据.xlsx', sheet_name='定检计划')
    fleet = pd.read_excel('data/数据.xlsx', sheet_name='机队数据')
    repair_data = pd.read_excel('data/数据.xlsx', sheet_name='送修合同清单')
    repair_record = pd.read_excel('data/修理次数13_长表.xlsx').drop_duplicates('序号', keep='last')
    whole_tsn = pd.read_excel('data/数据.xlsx', sheet_name='设备计量信息')

    airport_mapping = {
        "KMG": {"name": "昆明长水国际机场", "location": "昆明"},
        "HKT": {"name": "普吉国际机场", "location": "普吉岛"},
        "TFU": {"name": "成都天府国际机场", "location": "成都"},
        "CZX": {"name": "常州奔牛国际机场", "location": "常州"},
        "TAO": {"name": "青岛胶东国际机场", "location": "青岛"},
        "SGN": {"name": "新山一国际机场", "location": "胡志明市"},
        "SHA": {"name": "上海虹桥国际机场", "location": "上海"},
        "NKG": {"name": "南京禄口国际机场", "location": "南京"},
        "BHY": {"name": "北海福成机场", "location": "北海"},
        "DSN": {"name": "达卡沙阿贾拉勒国际机场", "location": "达卡"},
        "WUH": {"name": "武汉天河国际机场", "location": "武汉"},
        "HRB": {"name": "哈尔滨太平国际机场", "location": "哈尔滨"},
        "PKX": {"name": "北京大兴国际机场", "location": "北京"},
        "WNZ": {"name": "温州龙湾国际机场", "location": "温州"},
        "CGQ": {"name": "长春龙嘉国际机场", "location": "长春"},
        "DLC": {"name": "大连周水子国际机场", "location": "大连"},
        "PVG": {"name": "上海浦东国际机场", "location": "上海"},
        "BKK": {"name": "素万那普国际机场", "location": "曼谷"},
        "CSX": {"name": "长沙黄花国际机场", "location": "长沙"},
        "JMJ": {"name": "佳木斯东郊机场", "location": "佳木斯"},
        "SHE": {"name": "沈阳桃仙国际机场", "location": "沈阳"},
        "PNH": {"name": "金边国际机场", "location": "金边"},
        "JHG": {"name": "丽江三义国际机场", "location": "丽江"},
        "CAN": {"name": "广州白云国际机场", "location": "广州"},
        "YNT": {"name": "烟台蓬莱国际机场", "location": "烟台"},
        "YCU": {"name": "运城张孝机场", "location": "运城"},
        "BAV": {"name": "包头东河机场", "location": "包头"},
        "SWA": {"name": "汕头外砂机场", "location": "汕头"},
        "TYN": {"name": "太原武宿国际机场", "location": "太原"},
        "ZAT": {"name": "昭通机场", "location": "昭通"},
        "KHN": {"name": "南昌昌北国际机场", "location": "南昌"},
        "HAN": {"name": "河内内排国际机场", "location": "河内"},
        "CWJ": {"name": "朝阳机场", "location": "朝阳"},
        "INC": {"name": "银川河东国际机场", "location": "银川"},
        "BSD": {"name": "保山云瑞机场", "location": "保山"},
        "XIY": {"name": "西安咸阳国际机场", "location": "西安"},
        "XNN": {"name": "西宁曹家堡国际机场", "location": "西宁"},
        "SZX": {"name": "深圳宝安国际机场", "location": "深圳"},
        "NNG": {"name": "南宁吴圩国际机场", "location": "南宁"},
        "DXB": {"name": "迪拜国际机场", "location": "迪拜"},
        "LNJ": {"name": "临沧机场", "location": "临沧"},
        "HKG": {"name": "香港国际机场", "location": "香港"},
        "LUM": {"name": "卢萨卡国际机场", "location": "卢萨卡"},
        "LHW": {"name": "兰州中川国际机场", "location": "兰州"},
        "FUO": {"name": "佛山沙堤机场", "location": "佛山"},
        "TCZ": {"name": "台州路桥机场", "location": "台州"},
        "KRY": {"name": "克里米亚国际机场", "location": "辛菲罗波尔"},
        "DIG": {"name": "迪庆香格里拉机场", "location": "香格里拉"},
        "CGO": {"name": "郑州新郑国际机场", "location": "郑州"},
        "TSN": {"name": "天津滨海国际机场", "location": "天津"},
        "KIX": {"name": "大阪关西国际机场", "location": "大阪"},
        "HFE": {"name": "合肥新桥国际机场", "location": "合肥"},
        "FUK": {"name": "福冈国际机场", "location": "福冈"},
        "HET": {"name": "呼和浩特白塔国际机场", "location": "呼和浩特"},
        "LJG": {"name": "丽江三义国际机场", "location": "丽江"},
        "FUG": {"name": "阜阳西关机场", "location": "阜阳"},
        "YUS": {"name": "玉树巴塘机场", "location": "玉树"},
        "KWE": {"name": "贵阳龙洞堡国际机场", "location": "贵阳"},
        "HGH": {"name": "杭州萧山国际机场", "location": "杭州"},
        "WEH": {"name": "威海大水泊国际机场", "location": "威海"},
        "NGB": {"name": "宁波栎社国际机场", "location": "宁波"},
        "DAT": {"name": "大同云冈机场", "location": "大同"},
        "CKG": {"name": "重庆江北国际机场", "location": "重庆"},
        "CTU": {"name": "成都双流国际机场", "location": "成都"},
        "FOC": {"name": "福州长乐国际机场", "location": "福州"},
        "XMN": {"name": "厦门高崎国际机场", "location": "厦门"},
        "TNA": {"name": "济南遥墙国际机场", "location": "济南"},
        "HUZ": {"name": "惠州平潭机场", "location": "惠州"},
        "WMT": {"name": "武夷山机场", "location": "武夷山"},
        "SQJ": {"name": "黔江武陵山机场", "location": "黔江"},
        "SYX": {"name": "三亚凤凰国际机场", "location": "三亚"},
        "AKA": {"name": "赤坎国际机场", "location": "赤坎"},
        "ZHA": {"name": "湛江机场", "location": "湛江"},
        "ZUH": {"name": "珠海金湾国际机场", "location": "珠海"},
        "ENY": {"name": "恩施许家坪机场", "location": "恩施"},
        "HEK": {"name": "黑河瑷珲机场", "location": "黑河"},
        "YNJ": {"name": "延吉朝阳川国际机场", "location": "延吉"},
        "CJU": {"name": "济州国际机场", "location": "济州岛"},
        "ACX": {"name": "阿尔山机场", "location": "阿尔山"},
        "ICN": {"name": "仁川国际机场", "location": "首尔"},
        "JJN": {"name": "晋江国际机场", "location": "泉州"},
        "YIN": {"name": "伊宁机场", "location": "伊宁"},
        "LYI": {"name": "临沂启阳机场", "location": "临沂"},
        "BPE": {"name": "宜宾五粮液机场", "location": "宜宾"},
        "HAK": {"name": "海口美兰国际机场", "location": "海口"},
        "TVS": {"name": "台束机场", "location": "台束"},
        "DDG": {"name": "丹东浪头机场", "location": "丹东"},
        "WDS": {"name": "伊春林都机场", "location": "伊春"},
        "WXN": {"name": "万州五桥机场", "location": "万州"},
        "TOY": {"name": "丰桥机场", "location": "丰桥"},
        "YIH": {"name": "宜昌三峡机场", "location": "宜昌"},
        "TSA": {"name": "台北松山机场", "location": "台北"},
        "PUS": {"name": "釜山金海国际机场", "location": "釜山"},
        "BKI": {"name": "亚庇国际机场", "location": "亚庇"},
        "RIZ": {"name": "日喀则和平机场", "location": "日喀则"},
        "HLD": {"name": "海拉尔东山国际机场", "location": "呼伦贝尔"},
        "WUT": {"name": "武夷山机场", "location": "武夷山"},
        "JXA": {"name": "鸡西兴凯湖机场", "location": "鸡西"},
        "JGS": {"name": "井冈山机场", "location": "吉安"},
        "DYG": {"name": "张家界荷花国际机场", "location": "张家界"},
        "KWL": {"name": "桂林两江国际机场", "location": "桂林"},
        "WUZ": {"name": "梧州西江机场", "location": "梧州"},
        "HNY": {"name": "衡阳南岳机场", "location": "衡阳"},
        "HSN": {"name": "黄山屯溪国际机场", "location": "黄山"},
        "HIA": {"name": "夏威夷国际机场", "location": "檀香山"},
        "NGO": {"name": "名古屋中部国际机场", "location": "名古屋"},
        "AOG": {"name": "奥古斯塔国际机场", "location": "奥古斯塔"},
        "MIG": {"name": "米子机场", "location": "米子"},
        "MFM": {"name": "澳门国际机场", "location": "澳门"},
        "URC": {"name": "乌鲁木齐地窝堡国际机场", "location": "乌鲁木齐"},
        "KUL": {"name": "吉隆坡国际机场", "location": "吉隆坡"},
        "PEN": {"name": "槟城国际机场", "location": "槟城"},
        "HND": {"name": "东京羽田国际机场", "location": "东京"},
        "GMP": {"name": "金浦国际机场", "location": "首尔"},
        "BUD": {"name": "布达佩斯李斯特 Ferenc 国际机场", "location": "布达佩斯"},
        "MEL": {"name": "墨尔本国际机场", "location": "墨尔本"},
        "MRS": {"name": "马赛普罗旺斯国际机场", "location": "马赛"},
        "CMN": {"name": "穆罕默德五世国际机场", "location": "卡萨布兰卡"},
        "KOW": {"name": "赣州黄金机场", "location": "赣州"},
        "TAE": {"name": "大邱国际机场", "location": "大邱"},
        "WHA": {"name": "武汉天河国际机场", "location": "武汉"},
        "LZH": {"name": "柳州白莲机场", "location": "柳州"},
        "LDS": {"name": "六盘水月照机场", "location": "六盘水"},
        "TNH": {"name": "铜仁凤凰机场", "location": "铜仁"},
        "YBP": {"name": "宜宾五粮液机场", "location": "宜宾"},
        "AMS": {"name": "阿姆斯特丹史基浦机场", "location": "阿姆斯特丹"},
        "FRA": {"name": "法兰克福国际机场", "location": "法兰克福"},
        "STN": {"name": "伦敦斯坦斯特德机场", "location": "伦敦"},
        "LAX": {"name": "洛杉矶国际机场", "location": "洛杉矶"},
        "TPE": {"name": "台北桃园国际机场", "location": "台北"},
        "NRT": {"name": "东京成田国际机场", "location": "东京"},
        "SIN": {"name": "新加坡樟宜国际机场", "location": "新加坡"},
        "ANC": {"name": "安克雷奇国际机场", "location": "安克雷奇"},
        "ORD": {"name": "芝加哥奥黑尔国际机场", "location": "芝加哥"},
        "JFK": {"name": "约翰·F·肯尼迪国际机场", "location": "纽约"},
        "ENH": {"name": "恩施许家坪机场", "location": "恩施"},
        "JGN": {"name": "嘉峪关机场", "location": "嘉峪关"},
        "DLU": {"name": "大理机场", "location": "大理"},
        "LXA": {"name": "拉萨贡嘎国际机场", "location": "拉萨"},
        "KTM": {"name": "加德满都特里布万国际机场", "location": "加德满都"},
        "LPQ": {"name": "澜沧景迈机场", "location": "澜沧"},
        "YIW": {"name": "义乌机场", "location": "义乌"},
        "NLH": {"name": "尼尔基通用机场", "location": "尼尔基"},
        "SAI": {"name": "塞舌尔国际机场", "location": "维多利亚"},
        "CNX": {"name": "清迈国际机场", "location": "清迈"},
        "SYM": {"name": "沈阳桃仙国际机场", "location": "沈阳"},
        "RGN": {"name": "仰光国际机场", "location": "仰光"},
        "HDG": {"name": "胡志明市新山一国际机场", "location": "胡志明市"},
        "VTE": {"name": "万象瓦岱国际机场", "location": "万象"},
        "LZO": {"name": "临沧机场", "location": "临沧"},
        "DAC": {"name": "达卡沙阿贾拉勒国际机场", "location": "达卡"},
        "CMB": {"name": "科伦坡班达拉奈克国际机场", "location": "科伦坡"},
        "MXZ": {"name": "梅州梅县机场", "location": "梅州"},
        "DOY": {"name": "东营胜利机场", "location": "东营"},
        "ZYI": {"name": "遵义新舟机场", "location": "遵义"},
        "HZG": {"name": "杭州萧山国际机场", "location": "杭州"},
        "HJJ": {"name": "怀化芷江机场", "location": "怀化"},
        "LLV": {"name": "吕梁大武机场", "location": "吕梁"},
        "DBC": {"name": "白城长安机场", "location": "白城"},
        "YSQ": {"name": "伊春林都机场", "location": "伊春"},
        "TEN": {"name": "铜仁凤凰机场", "location": "铜仁"},
        "JUH": {"name": "吉达阿卜杜勒-阿齐兹国王国际机场", "location": "吉达"},
        "VVO": {"name": "符拉迪沃斯托克国际机场", "location": "符拉迪沃斯托克"},
        "UYN": {"name": "乌兰巴托成吉思汗国际机场", "location": "乌兰巴托"},
        "LLF": {"name": "临汾尧都机场", "location": "临汾"},
        "SJW": {"name": "石家庄正定国际机场", "location": "石家庄"},
        "JNZ": {"name": "锦州湾机场", "location": "锦州"},
        "HCZ": {"name": "河池金城江机场", "location": "河池"},
        "HLH": {"name": "乌兰浩特义勒力特机场", "location": "乌兰浩特"},
        "TLQ": {"name": "吐鲁番交河机场", "location": "吐鲁番"},
        "AVA": {"name": "阿瓦提机场", "location": "阿瓦提"},
        "HYN": {"name": "衡阳南岳机场", "location": "衡阳"},
        "KCA": {"name": "库车龟兹机场", "location": "库车"},
        "DXJ": {"name": "大兴安岭鄂伦春机场", "location": "加格达奇"},
        "YZY": {"name": "永州零陵机场", "location": "永州"},
        "DNH": {"name": "丹东浪头机场", "location": "丹东"},
        "JGD": {"name": "井冈山机场", "location": "吉安"},
        "OKJ": {"name": "鄂尔多斯伊金霍洛国际机场", "location": "鄂尔多斯"},
        "NAO": {"name": "南充高坪机场", "location": "南充"},
        "AEB": {"name": "百色巴马机场", "location": "百色"},
        "KMQ": {"name": "昆明长水国际机场", "location": "昆明"},
        "CHG": {"name": "朝阳机场", "location": "朝阳"},
        "HIJ": {"name": "哈密机场", "location": "哈密"},
        "NGS": {"name": "井冈山机场", "location": "吉安"},
        "KIJ": {"name": "延吉朝阳川国际机场", "location": "延吉"},
        "DZH": {"name": "达州金垭机场", "location": "达州"},
        "DQA": {"name": "大庆萨尔图机场", "location": "大庆"},
        "OKA": {"name": "冲绳那霸国际机场", "location": "那霸"},
        "PEK": {"name": "北京首都国际机场", "location": "北京"},
        "MNL": {"name": "尼诺伊·阿基诺国际机场", "location": "马尼拉"},
        "CTS": {"name": "札幌新千岁国际机场", "location": "札幌"},
        "LYA": {"name": "洛阳北郊机场", "location": "洛阳"},
        "HTN": {"name": "和田机场", "location": "和田"},
        "NDG": {"name": "齐齐哈尔三家子机场", "location": "齐齐哈尔"},
        "KHG": {"name": "喀什国际机场", "location": "喀什"},
        "AAT": {"name": "阿勒泰机场", "location": "阿勒泰"},
        "FSZ": {"name": "佛山沙堤机场", "location": "佛山"},
        "CEB": {"name": "宿务麦克坦-宿务国际机场", "location": "宿务"},
        "XIC": {"name": "西昌青山机场", "location": "西昌"},
        "KRL": {"name": "库尔勒机场", "location": "库尔勒"},
        "KOJ": {"name": "鹿儿岛机场", "location": "鹿儿岛"},
        "WUA": {"name": "乌海机场", "location": "乌海"},
        "JMU": {"name": "佳木斯东郊机场", "location": "佳木斯"},
        "BAR": {"name": "巴尔的摩-华盛顿国际机场", "location": "巴尔的摩"},
        "SVO": {"name": "莫斯科谢列梅捷沃国际机场", "location": "莫斯科"},
        "LGW": {"name": "伦敦盖特威克机场", "location": "伦敦"},
        "AUH": {"name": "阿布扎比国际机场", "location": "阿布扎比"},
        "LED": {"name": "圣彼得堡普尔科沃机场", "location": "圣彼得堡"},
        "RUH": {"name": "利雅得哈立德国王国际机场", "location": "利雅得"},
        "KZN": {"name": "喀山国际机场", "location": "喀山"},
        "CGK": {"name": "雅加达苏加诺-哈达国际机场", "location": "雅加达"},
        "IST": {"name": "伊斯坦布尔阿塔图尔克国际机场", "location": "伊斯坦布尔"},
        "SYD": {"name": "悉尼金斯福德·史密斯国际机场", "location": "悉尼"},
        "MLE": {"name": "马累国际机场", "location": "马累"},
        "MXP": {"name": "米兰马尔彭萨国际机场", "location": "米兰"},
        "AKL": {"name": "奥克兰国际机场", "location": "奥克兰"},
        "BNE": {"name": "布里斯班国际机场", "location": "布里斯班"},
        "CAI": {"name": "开罗国际机场", "location": "开罗"},
        "MAD": {"name": "马德里巴拉哈斯国际机场", "location": "马德里"},
        "VCE": {"name": "威尼斯马可·波罗国际机场", "location": "威尼斯"},
        "FCO": {"name": "罗马菲乌米奇诺国际机场", "location": "罗马"},
        "YYZ": {"name": "多伦多皮尔逊国际机场", "location": "多伦多"},
        "LHR": {"name": "伦敦希思罗机场", "location": "伦敦"},
        "CDG": {"name": "巴黎夏尔·戴高乐国际机场", "location": "巴黎"},
        "SFO": {"name": "旧金山国际机场", "location": "旧金山"},
        "LYG": {"name": "连云港白塔埠机场", "location": "连云港"},
        "NTG": {"name": "南通兴东国际机场", "location": "南通"},
        "KHH": {"name": "高雄国际机场", "location": "高雄"},
        "WUX": {"name": "无锡硕放国际机场", "location": "无锡"},
        "RMQ": {"name": "日喀则和平机场", "location": "日喀则"},
        "DPS": {"name": "登巴萨伍拉莱国际机场", "location": "巴厘岛"},
        "MED": {"name": "麦地那穆罕默德·本·阿卜杜勒-阿齐兹王子国际机场", "location": "麦地那"},
        "BPL": {"name": "博帕尔机场", "location": "博帕尔"},
        "AKU": {"name": "阿克苏温宿机场", "location": "阿克苏"},
        "GOQ": {"name": "果洛玛沁机场", "location": "果洛"},
        "HXD": {"name": "海西德令哈机场", "location": "德令哈"},
        "HTT": {"name": "花土沟机场", "location": "茫崖"},
        "RKZ": {"name": "若羌楼兰机场", "location": "若羌"},
        "QSZ": {"name": "衢州机场", "location": "衢州"},
        "GMQ": {"name": "固原六盘山机场", "location": "固原"},
        "HBQ": {"name": "淮安涟水国际机场", "location": "淮安"},
        "JIC": {"name": "鸡西兴凯湖机场", "location": "鸡西"},
        "TCG": {"name": "塔城民航机场", "location": "塔城"},
        "NGQ": {"name": "那曲达仁机场", "location": "那曲"},
        "PQC": {"name": "攀枝花保安营机场", "location": "攀枝花"},
        "SHS": {"name": "石河子花园机场", "location": "石河子"},
        "YNZ": {"name": "盐城国际机场", "location": "盐城"},
        "EHU": {"name": "额济纳旗桃来机场", "location": "额济纳旗"}
    }

    fleet_mapping = [
        ['SD', '技术山东分公司', '济南'],
        ['PD', '技术浦东维修基地', '上海'],
        ['SC', '技术四川分公司', '成都'],
        ['JS', '技术江苏分公司', '南京'],
        ['XA', '技术西北分公司', '西安'],
        ['GS', '技术甘肃分公司', '兰州'],
        ['JX', '技术江西分公司', '南昌'],
        ['ZJ', '技术浙江分公司', '宁波'],
        ['HQ', '技术虹桥维修基地', '上海'],
        ['HH', '技术浦东维修基地', '上海'],
        ['KM', '技术云南分公司', '昆明'],
        ['WB', '技术武汉分公司', '武汉'],
        ['WH', '技术武汉分公司', '武汉'],
        ['HB', '技术河北分公司', '石家庄'],
        ['SX', '技术山西分公司', '太原'],
        ['SP', '技术浦东维修基地', '上海'],
        ['BJ', '技术北京分公司', '北京'],
        ['AH', '技术安徽分公司', '合肥'],
        ['SH', '技术虹桥维修基地', '上海'],
        ['NY', '技术北京分公司', '北京'],
        ['ZJ', '技术山东分公司', '济南'],
        ['GD', '技术广东分公司', '广州'],
        ['XA', '技术浦东维修基地', '上海'],
        ['DR', None, None],
        ['XM', '技术安徽分公司', '合肥'],
        ['HQ', '技术西北分公司', '西安'],
        ['XA', '技术虹桥维修基地', '上海'],
        ['BJ', '技术虹桥维修基地', '上海'],
        ['RL', None, None],
        ['PD', '技术安徽分公司', '合肥'],
        ['GJ', '技术国产飞机部', '上海'],
        ['XA', '技术甘肃分公司', '兰州'],
        ['HQ', '技术安徽分公司', '合肥'],
        ['SD', '技术浙江分公司', '宁波']
    ]

    serial_json = install_idg_info(idg_rec, store_data, storage, airplane, flight_data, fleet, repair_data,
                                   repair_record, whole_tsn, airport_mapping, fleet_mapping)

    # 转换为DataFrame
    df = pd.DataFrame.from_dict(serial_json, orient='index').reset_index()
    df = df.rename(columns={'index': '序号'})

    # 保存为Excel文件
    output_file = "data/当前装机号详情.xlsx"
    df.to_excel(output_file, index=False, sheet_name='装机号详情')

    print(f"结果已保存到: {output_file}")
    print(f"共处理 {len(df)} 条记录")
