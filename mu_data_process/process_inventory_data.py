import pandas as pd


def process_inventory_data(store_data, storage, df=None):
    """
    处理库存数据并生成各物料在不同位置的库存统计报表

    参数:
        store_data: 包含库存信息的DataFrame
        storage: 包含库存地信息的DataFrame
        df: 用于筛选的参照DataFrame，默认为None

    返回:
        pivot_df: 处理后的透视表DataFrame
    """
    # 位置映射字典
    loc_mapping = {
        "SD": '济南',
        "SH": '上海',
        "GS": '甘肃',
        "ZJ": '浙江',
        "JS": '江苏',
        "BJ": '北京',
        "XA": '西安',
        "JX": '江西',
        "AH": '安徽',
        "WH": '武汉',
        "NY": '北京南苑',
        "GD": '广东',
        "SX": '山西',
        "KM": '昆明',
        "HB": '河北'
    }

    # 处理库存地信息，创建库存地字典
    store_dict = {
        row['库存地代码']: {
            '库存地': row['库存地'],
            '库存地类别': row['库存地类别']
        } for _, row in storage.iterrows()
    }

    # 添加新列
    df_processed = store_data.copy()
    df_processed[['状态', '库存地', '库存地类别', '位置']] = None  # 添加新的'位置'列

    # 处理每条记录，确定状态和位置信息
    for idx, row in df_processed.iterrows():
        if row['系统状态'] == 'ESTO':
            if pd.isna(row['特殊库存']):
                status = '在厂家库存地' if row['库存地点'] == 'GYS1' else '在库'
            else:
                status = '在修' if row['特殊库存'] == 'O' else '其他库存类型'
        elif row['系统状态'] == 'ASEQ':
            status = '安装'
        else:
            status = '其他库存类型'

        # 检查库存地点是否为空，为空则使用维护工厂
        key = row['库存地点'] if not pd.isna(row['库存地点']) else row['维护工厂']
        loc_info = store_dict.get(key, {})

        # 根据key的前两个字母映射位置
        location_key = key[:2] if isinstance(key, str) else None
        location = loc_mapping.get(location_key) if location_key else None

        df_processed.loc[idx, ['状态', '库存地', '库存地类别', '位置']] = [
            status, loc_info.get('库存地'), loc_info.get('库存地类别'), location
        ]

    # 筛选数据：不在参照df的序列号且状态为安装或在库
    if df is not None and '序号' in df.columns:
        exclude_serials = list(df['序号'].unique())
        in_store_data = df_processed[
            (~df_processed['序列号'].isin(exclude_serials)) &
            (df_processed['状态'].isin(['安装', '在库']))
            ].reset_index(drop=True)
    else:
        # 如果没有提供df或df中没有'序号'列，只按状态筛选
        in_store_data = df_processed[
            df_processed['状态'].isin(['安装', '在库'])
        ].reset_index(drop=True)

    # 使用pivot_table来统计每个物料在各个位置的出现次数
    pivot_df = in_store_data.pivot_table(
        index='物料',
        columns='位置',
        aggfunc='size',
        fill_value=0
    ).reset_index()

    # 添加物料计数总计列
    pivot_df['总计'] = pivot_df.iloc[:, 1:].sum(axis=1)

    return pivot_df


if __name__ == "__main__":
    # 读取数据
    store_data = pd.read_excel('data/数据.xlsx', sheet_name='设备清单及库存')
    storage = pd.read_excel('data/库存地类别.xlsx')

    # 处理数据
    result_df = process_inventory_data(store_data, storage)

    # 保存为Excel文件
    output_file = "data/库存情况.xlsx"
    result_df.to_excel(output_file, index=False, sheet_name='库存统计')

    print(f"库存统计结果已保存到: {output_file}")
    print(f"共统计 {len(result_df)} 种物料的库存情况")