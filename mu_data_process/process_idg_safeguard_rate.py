import pandas as pd
from sqlalchemy import create_engine, text

from mu_air_fore.config.db_settings import SQLALCHEMY_URL


def process_idg_safeguard_rate(data, aog):
    """
    处理idg装机履历，计算自有器材保障率

    参数:
        data: idg装机履历的DataFrame
        aog: 航司求援信息的DataFrame

    返回:
        每个件号各年度自有器材保障率
    """
    # 读取IDG履历数据
    data.replace('yyy', None, inplace=True)
    data.replace('xxx', None, inplace=True)
    data['年'] = data['时间'].dt.year

    # 提取idg1和idg2数据并合并
    idg1 = data[['机号', '时间', '年', 'idg1件号', 'idg1', 'idg1变化标记', 'idg1过账日期', 'idg1物料凭证',
                 'idg1领料用途']].dropna(subset='idg1件号')
    idg2 = data[['机号', '时间', '年', 'idg2件号', 'idg2', 'idg2变化标记', 'idg2过账日期', 'idg2物料凭证',
                 'idg2领料用途']].dropna(subset='idg2件号')

    idg1.columns = ['机号', '时间', '年', 'idg件号', 'idg序号', 'idg变化标记', 'idg过账日期', 'idg物料凭证',
                    'idg领料用途']
    idg2.columns = ['机号', '时间', '年', 'idg件号', 'idg序号', 'idg变化标记', 'idg过账日期', 'idg物料凭证',
                    'idg领料用途']

    idg = pd.concat([idg1, idg2], axis=0, ignore_index=True)
    idg = idg[idg['idg变化标记'] == '是'].reset_index(drop=True)

    # 标记AOG出库记录
    def mark_aog_records(aog_df, idg_df):
        idg_df['可用库出库'] = idg_df['idg物料凭证'].notna().map({True: '是', False: '否'})
        idg_df['AOG'] = '否'

        for idx, row in idg_df.iterrows():
            if row['idg序号'] in aog_df['序列号'].values:
                aog_record = aog_df[aog_df['序列号'] == row['idg序号']].iloc[0]
                if aog_record['入库日期'] <= row['时间'] <= aog_record['归还过账日期']:
                    idg_df.at[idx, 'AOG'] = '是'

        idg_df.drop(['idg过账日期', 'idg物料凭证', 'idg领料用途'], axis=1, inplace=True)
        return idg_df

    idg = mark_aog_records(aog, idg)

    # 按件号和年份分组计算指标
    grouped = idg.groupby(['idg件号', '年'])
    result = grouped.agg(
        安装总量=('idg变化标记', lambda x: (x == '是').sum()),
        可用库出库量=('可用库出库', lambda x: (x == '是').sum()),
        AOG保障量=('AOG', lambda x: (x == '是').sum())
    ).reset_index()

    # 计算自有库存出库数量和自有器材保障率
    result['串件安装总量'] = result['安装总量'] - result['可用库出库量']
    result['自有器材可用库安装发料数量'] = result['可用库出库量'] - result['AOG保障量']
    result['自有器材保障率'] = (result['自有器材可用库安装发料数量'] / result['安装总量'] * 100).round(2)

    # 筛选2016年及以后的数据
    result = result[result['年'] >= 2016].reset_index(drop=True)

    return result


def save_guarantee_rate_to_mysql(result_df: pd.DataFrame) -> None:
    """
    将自有器材保障率结果数据写入 MySQL guarantee_rate_res 表

    参数:
        result_df: 包含保障率统计数据的 DataFrame
    """
    # 创建数据库连接
    engine = create_engine(SQLALCHEMY_URL, echo=False)

    # 复制数据框以避免修改原始数据
    df_to_save = result_df.copy()

    # 字段映射 - 将中文列名映射到数据库字段名
    column_mapping = {
        'idg件号': 'pnr',
        '年': 'year',
        '安装总量': 'azzl',
        '可用库出库量': 'kykckl',
        'AOG保障量': 'aogbzl',
        '串件安装总量': 'cjazzl',
        '自有器材可用库安装发料数量': 'zyqckykazflsl',
        '自有器材保障率': 'zyqcbzl'
    }

    # 重命名列
    df_to_save.rename(columns=column_mapping, inplace=True)

    # 选择需要保存到数据库的列
    columns_to_save = list(column_mapping.values())
    df_to_save = df_to_save[columns_to_save]

    # 写入数据库
    df_to_save.to_sql(
        name='guarantee_rate_res',
        con=engine,
        if_exists='append',
        index=False,
        chunksize=2000,
        method='multi'
    )

    # 输出反馈信息
    with engine.begin() as conn:
        total = conn.execute(text("SELECT COUNT(*) FROM `guarantee_rate_res`")).scalar()
    print(f"[DB] guarantee_rate_res 导入完成，新增 {len(df_to_save)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    # 读取数据
    # idg_data = pd.read_excel('data/IDG履历_关联出库.xlsx')
    idg_data = pd.read_excel('/home/<USER>/mu_air_python/mu_air_python/data/航材出库履历.xlsx')
    # aog_data = pd.read_excel('data/数据.xlsx', sheet_name='借入清单')
    aog_data = pd.read_excel('/home/<USER>/mu_air_python/mu_air_python/data/借入清单.xlsx')

    # 处理数据
    result_df = process_idg_safeguard_rate(idg_data, aog_data)

    # 保存为Excel文件
    output_file = "data/自有器材保障率.xlsx"
    result_df.to_excel(output_file, index=False, sheet_name='保障率统计')

    # 保存到MySQL数据库
    save_guarantee_rate_to_mysql(result_df)

    print(f"自有器材保障率数据已保存到: {output_file}")
    print(f"共统计 {len(result_df)} 条保障率记录")