import pandas as pd


def calculate_durations(df):
    # 确保日期列是datetime类型
    date_columns = ['入库日期', '拆下日期', '系统拆下日期', '合同日期', '出库日期', '签收日期', '检验决策日期']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col])

    # 生成整合退库日期列
    df['整合退库日期'] = df['退库日期'].fillna(df['系统拆下日期']).fillna(df['拆下日期'])

    # 计算各时长列
    if '合同日期' in df.columns and '整合退库日期' in df.columns:
        df['送修采购消耗时长'] = (df['合同日期'] - df['整合退库日期']).dt.days

    if '签收日期' in df.columns and '出库日期' in df.columns:
        df['修理时长'] = (df['签收日期'] - df['出库日期']).dt.days

    if '检验决策日期' in df.columns and '签收日期' in df.columns:
        df['入可用库消耗时长'] = (df['检验决策日期'] - df['签收日期']).dt.days

    df['维修总时长'] = df['送修采购消耗时长'] + df['修理时长'] + df['入可用库消耗时长']

    return df


if __name__ == "__main__":
    # 读取数据
    data = pd.read_excel("data/数据.xlsx", sheet_name='送修合同清单')

    # 计算时长
    result_df = calculate_durations(data)

    # 保存为Excel文件
    output_file = "data/维修时长_计算结果.xlsx"
    result_df.to_excel(output_file, index=False, sheet_name='计算结果')

    print(f"计算结果已保存到: {output_file}")