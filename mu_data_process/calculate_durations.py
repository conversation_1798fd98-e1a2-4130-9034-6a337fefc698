import pandas as pd
from sqlalchemy import create_engine, text
from mu_air_fore.config.db_settings import SQLALCHEMY_URL


def calculate_durations(df):
    # 确保日期列是datetime类型
    date_columns = ['入库日期', '拆下日期', '系统拆下日期', '合同日期', '出库日期', '签收日期', '检验决策日期']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col])

    # 生成整合退库日期列
    df['整合退库日期'] = df['退库日期'].fillna(df['系统拆下日期']).fillna(df['拆下日期'])

    # 计算各时长列
    if '合同日期' in df.columns and '整合退库日期' in df.columns:
        df['送修采购消耗时长'] = (df['合同日期'] - df['整合退库日期']).dt.days

    if '签收日期' in df.columns and '出库日期' in df.columns:
        df['修理时长'] = (df['签收日期'] - df['出库日期']).dt.days

    if '检验决策日期' in df.columns and '签收日期' in df.columns:
        df['入可用库消耗时长'] = (df['检验决策日期'] - df['签收日期']).dt.days

    df['维修总时长'] = df['送修采购消耗时长'] + df['修理时长'] + df['入可用库消耗时长']

    return df


def save_durations_to_mysql(result_df: pd.DataFrame) -> None:
    """
    将维修时长计算结果写入 MySQL tat_duration_calculation 表

    参数:
        result_df: 包含维修时长计算结果的 DataFrame
    """
    # 创建数据库连接
    engine = create_engine(SQLALCHEMY_URL, echo=False)

    # 复制数据框以避免修改原始数据
    df_to_save = result_df.copy()

    # 字段映射 - 将列名映射到数据库字段名
    column_mapping = {
        '件号': 'pnr',
        '序号': 'seq',
        '出库日期': 'ckrq',
        '修理时长': 'xlsc'
    }

    # 重命名列
    df_to_save.rename(columns=column_mapping, inplace=True)

    # 选择需要保存到数据库的列
    columns_to_save = list(column_mapping.values())
    # 确保只选择存在的列
    columns_to_save = [col for col in columns_to_save if col in df_to_save.columns]
    df_to_save = df_to_save[columns_to_save]

    # 处理日期格式，将出库日期转换为字符串格式
    if 'ckrq' in df_to_save.columns:
        df_to_save['ckrq'] = df_to_save['ckrq'].dt.strftime('%Y-%m-%d')

    # 先清空表中的所有数据
    with engine.begin() as conn:
        conn.execute(text("TRUNCATE TABLE `tat_duration_calculation`"))

    # 写入数据库
    df_to_save.to_sql(
        name='tat_duration_calculation',
        con=engine,
        if_exists='append',
        index=False,
        chunksize=2000,
        method='multi'
    )

    # 输出反馈信息
    with engine.begin() as conn:
        total = conn.execute(text("SELECT COUNT(*) FROM `tat_duration_calculation`")).scalar()
    print(f"[DB] tat_duration_calculation 导入完成，新增 {len(df_to_save)} 行；当前表总记录 {total} 行.")


if __name__ == "__main__":
    # 读取数据
#     data = pd.read_excel("data/数据.xlsx", sheet_name='送修合同清单')
    data = pd.read_excel('/home/<USER>/mu_air_python/mu_air_python/data/送修合同清单.xlsx')

    # 计算时长
    result_df = calculate_durations(data)

    # 保存为Excel文件
    output_file = "data/维修时长_计算结果.xlsx"
    result_df.to_excel(output_file, index=False, sheet_name='计算结果')

    # 生成仅包含指定字段的Excel文件
    selected_columns = ['件号', '序号', '出库日期', '修理时长']
    simplified_df = result_df[selected_columns].copy()

    simplified_output_file = "data/维修时长.xlsx"
    simplified_df.to_excel(simplified_output_file, index=False, sheet_name='维修时长')

    # 保存到MySQL数据库
    save_durations_to_mysql(result_df)

    print(f"完整计算结果已保存到: {output_file}")
    print(f"简化版维修时长数据已保存到: {simplified_output_file}")
