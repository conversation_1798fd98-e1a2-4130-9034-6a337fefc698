#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KM生存曲线分析使用示例
演示如何使用修改后的 build_weighted_km_for_target_V1.py 脚本
"""

import subprocess
import sys
import json
from pathlib import Path

def run_km_analysis(tasks_config: dict, model_version: str = "v1.0", save_to_db: bool = True):
    """
    运行KM生存曲线分析
    
    Args:
        tasks_config: 任务配置字典，格式为 {"件号": {"目标序号": ["相似序号列表"]}}
        model_version: 模型版本
        save_to_db: 是否保存到数据库
    """
    print(f"\n{'='*60}")
    print(f"KM生存曲线分析")
    print(f"模型版本: {model_version}")
    print(f"保存到数据库: {'是' if save_to_db else '否'}")
    print(f"任务配置: {tasks_config}")
    print(f"{'='*60}")
    
    try:
        # 构建命令
        script_path = Path(__file__).parent / "build_weighted_km_for_target_V1.py"
        tasks_json = json.dumps(tasks_config, ensure_ascii=False)
        
        cmd = [sys.executable, str(script_path), tasks_json, model_version]
        if not save_to_db:
            cmd.append("--no-db")
        
        print(f"执行命令: {' '.join(cmd[:2])} '{tasks_json}' {model_version}")
        if not save_to_db:
            print("附加参数: --no-db")
        
        # 运行命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # 显示输出
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
        print("✅ KM分析成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ KM分析失败，错误代码: {e.returncode}")
        if e.stdout:
            print("输出:", e.stdout)
        if e.stderr:
            print("错误:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def example_single_task():
    """单个任务示例"""
    print("=== 单个任务示例 ===")
    
    tasks_config = {
        "1706903": {
            "AAB3001205": ["AAB3001251", "AAB3001762", "AAB3001761", "AAB3002140", "AAB3002003"]
        }
    }
    
    success = run_km_analysis(tasks_config, "v1.0", True)
    return success

def example_multiple_tasks():
    """多个任务示例"""
    print("=== 多个任务示例 ===")
    
    tasks_config = {
        "1706903": {
            "AAB3001205": ["AAB3001251", "AAB3001762"],
            "AAB3001251": ["AAB3001205", "AAB3001762"]
        },
        "740119G": {
            "SN001": ["SN002", "SN003"]
        }
    }
    
    success = run_km_analysis(tasks_config, "v2.0", True)
    return success

def example_no_database():
    """不保存到数据库示例"""
    print("=== 不保存到数据库示例 ===")
    
    tasks_config = {
        "1706903": {
            "AAB3001205": ["AAB3001251", "AAB3001762"]
        }
    }
    
    success = run_km_analysis(tasks_config, "test_v1.0", False)
    return success

def interactive_example():
    """交互式示例"""
    print("=== 交互式KM分析 ===")
    
    # 输入件号
    part_number = input("请输入件号: ").strip()
    if not part_number:
        print("件号不能为空")
        return False
    
    # 输入目标序号
    target_serial = input("请输入目标序号: ").strip()
    if not target_serial:
        print("目标序号不能为空")
        return False
    
    # 输入相似序号
    similar_serials_input = input("请输入相似序号（用逗号分隔）: ").strip()
    if not similar_serials_input:
        print("相似序号不能为空")
        return False
    
    similar_serials = [s.strip() for s in similar_serials_input.split(",") if s.strip()]
    
    # 输入模型版本
    model_version = input("请输入模型版本（默认v1.0）: ").strip()
    if not model_version:
        model_version = "v1.0"
    
    # 是否保存到数据库
    save_db_input = input("是否保存到数据库？(y/n，默认y): ").strip().lower()
    save_to_db = save_db_input != 'n'
    
    # 构建任务配置
    tasks_config = {
        part_number: {
            target_serial: similar_serials
        }
    }
    
    print(f"\n配置确认:")
    print(f"件号: {part_number}")
    print(f"目标序号: {target_serial}")
    print(f"相似序号: {similar_serials}")
    print(f"模型版本: {model_version}")
    print(f"保存到数据库: {'是' if save_to_db else '否'}")
    
    confirm = input("\n确认执行？(y/n): ").strip().lower()
    if confirm != 'y':
        print("取消执行")
        return False
    
    success = run_km_analysis(tasks_config, model_version, save_to_db)
    return success

def main():
    """主函数"""
    print("KM生存曲线分析工具")
    print("请选择运行模式:")
    print("1. 单个任务示例")
    print("2. 多个任务示例")
    print("3. 不保存到数据库示例")
    print("4. 交互式分析")
    print("5. 退出")
    
    choice = input("请选择 (1-5): ").strip()
    
    if choice == "1":
        example_single_task()
    elif choice == "2":
        example_multiple_tasks()
    elif choice == "3":
        example_no_database()
    elif choice == "4":
        interactive_example()
    elif choice == "5":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
