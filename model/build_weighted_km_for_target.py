# -*- coding: utf-8 -*-
"""
为指定 目标件号-目标序号 构建加权KM生存曲线（使用相似序号及其相似度作为权重）

功能：
1) 读取维修历史（与原 INPUT_FILE_PATH 相同格式）做必要的数据处理
2) 读取 飞机数据/按件号TSR预测与置信度分析结果_KM生存曲线.xlsx 的 “同件号高相似序号(>=0.7)” sheet
3) 根据输入的 目标件号-目标序号 与 相似序号 列表，从sheet查找相似度，构造 (相似序号, 相似度)
4) 复用 part_based_tsr_confidence_analyzer.py 的加权KM算法构建曲线，生成并保存：
   - 加权KM曲线数据表（含95%CI）
   - 加权KM曲线图（含95%CI阴影）
5) 输出目录：codes/件序号级TSR计算+置信度/test/KM_Curve_target/时间戳/

使用：
- 直接运行后按提示输入 目标件号、目标序号、相似序号（可用逗号分隔多个）。
- 如需固定参数，可将下方 DEFAULT_* 常量修改为你的路径。
"""
import os
import sys
import datetime
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt

# 使脚本可直接运行（无需安装为包）
CURRENT_DIR = Path(__file__).resolve().parent
if str(CURRENT_DIR) not in sys.path:
    sys.path.insert(0, str(CURRENT_DIR))

from part_based_tsr_confidence_analyzer import (
    PartBasedTSRConfidenceAnalyzer,
    INPUT_FILE_PATH as DEFAULT_REPAIR_HISTORY_FILE,
)

# 常量路径（可按需修改）
AIRCRAFT_DATA_DIR = CURRENT_DIR / "飞机数据"
SIMILARITY_EXCEL = AIRCRAFT_DATA_DIR / "按件号TSR预测与置信度分析结果_KM生存曲线.xlsx"
OUTPUT_BASE_DIR = CURRENT_DIR / "飞机数据" / "KM_Curve_target"

# 配置任务列表（可按需修改）
TASKS = [
    ("1706903", "AAB3001205", ["AAB3001251", "AAB3001762", "AAB3001761", "AAB3002140", "AAB3002003"]),
]


def _ensure_output_dir() -> Path:
    ts = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    out_dir = OUTPUT_BASE_DIR / ts
    out_dir.mkdir(parents=True, exist_ok=True)
    return out_dir


def _read_similarity_sheet(similarity_file: Path) -> pd.DataFrame:
    df = pd.read_excel(similarity_file, sheet_name='同件号高相似序号(>=0.7)')
    df.columns = df.columns.map(str).str.strip()
    # 期望列：目标件号, 目标序号, 相似序号, 相似度
    return df


def _get_similar_list(df_sim: pd.DataFrame, target_part: str, target_serial: str, sim_serials: list) -> list:
    # 过滤目标
    sub = df_sim[(df_sim['目标件号'] == target_part) & (df_sim['目标序号'].astype(str) == str(target_serial))]
    if len(sub) == 0:
        return []
    # 取相似序号与相似度
    sub['相似序号'] = sub['相似序号'].astype(str)
    wanted = [s.strip() for s in sim_serials if s and str(s).strip()]
    if wanted:
        sub = sub[sub['相似序号'].isin(wanted)]
    return [(row['相似序号'], float(row['相似度'])) for _, row in sub.iterrows()]


def _plot_km_with_ci(timeline, survival, ci_lower, ci_upper, title: str, save_path: Path):
    plt.figure(figsize=(8, 5))
    plt.plot(timeline, survival, label='加权KM生存曲线', color='#1f77b4', linewidth=2)
    if ci_lower is not None and ci_upper is not None:
        plt.fill_between(timeline, ci_lower, ci_upper, color='#1f77b4', alpha=0.15, label='95%置信区间')
    plt.xlabel('TSR')
    plt.ylabel('生存概率')
    plt.title(title)
    plt.grid(alpha=0.3)
    plt.legend()
    plt.tight_layout()
    plt.savefig(save_path, dpi=150)
    plt.close()


def build_weighted_km_for_target(
    repair_history_file: str = None,
    target_part_num: str = None,
    target_serial: str = None,
    similar_serials: list = None,
) -> Path:
    # 路径准备
    repair_history_path = repair_history_file or str(DEFAULT_REPAIR_HISTORY_FILE)
    similarity_file = SIMILARITY_EXCEL
    out_dir = _ensure_output_dir()

    print(f"📂 维修历史: {repair_history_path}")
    print(f"📄 相似序号文件: {similarity_file}")
    print(f"📁 输出目录: {out_dir}")

    # 读取维修历史并处理
    raw_df = pd.read_excel(repair_history_path)
    analyzer = PartBasedTSRConfidenceAnalyzer(enable_step1_prediction=False)
    print("🔄 处理维修历史数据...")
    processed_df = analyzer.tsr_analyzer.process_data(raw_df)

    # 读取相似度sheet
    print("🔄 读取相似序号sheet...")
    df_sim = _read_similarity_sheet(similarity_file)

    # 目标与相似序号解析
    if similar_serials is None:
        similar_serials = []
    sim_list = _get_similar_list(df_sim, str(target_part_num), str(target_serial), similar_serials)
    if not sim_list:
        raise ValueError("未找到匹配的相似序号与相似度，请检查目标/相似序号或sheet内容。")
    print(f"✅ 相似序号及相似度: {sim_list}")

    # 准备目标数据与件号数据
    part_df = processed_df[processed_df['件号'] == target_part_num].copy()
    target_data = part_df[part_df['序号'].astype(str) == str(target_serial)].sort_values('第几次维修').copy()
    if len(target_data) == 0:
        raise ValueError("目标序号在维修历史数据中不存在或无记录。")

    # 构建加权KM曲线（使用原脚本算法）
    print("🚀 构建加权KM生存曲线...")
    survival_curve = analyzer.build_weighted_km_curve_no_plot(
        target_serial=str(target_serial),
        target_data=target_data,
        similar_serials=sim_list,
        part_df=part_df,
    )
    if survival_curve is None:
        raise RuntimeError("加权KM曲线构建失败。")

    kmf = survival_curve['kmf']
    timeline = kmf.survival_function_.index.values
    survival = kmf.survival_function_['KM_estimate'].values
    ci_lower = None
    ci_upper = None
    if hasattr(kmf, 'confidence_interval_') and kmf.confidence_interval_ is not None:
        ci_df = kmf.confidence_interval_
        if 'KM_estimate_lower_0.95' in ci_df.columns and 'KM_estimate_upper_0.95' in ci_df.columns:
            ci_lower = ci_df['KM_estimate_lower_0.95'].values
            ci_upper = ci_df['KM_estimate_upper_0.95'].values

    # 保存数据表
    df_out = pd.DataFrame({
        'timeline': timeline,
        'survival_probability': survival,
        'ci_lower_0.95': ci_lower if ci_lower is not None else [None]*len(timeline),
        'ci_upper_0.95': ci_upper if ci_upper is not None else [None]*len(timeline),
    })
    excel_path = out_dir / f"加权KM_{target_part_num}_{target_serial}.xlsx"
    df_out.to_excel(excel_path, index=False)
    print(f"✅ 已保存KM表: {excel_path}")

    # 保存图表
    title = f"加权KM曲线 - 件号 {target_part_num} / 序号 {target_serial}"
    img_path = out_dir / f"加权KM_{target_part_num}_{target_serial}.png"
    _plot_km_with_ci(timeline, survival, ci_lower, ci_upper, title, img_path)
    print(f"✅ 已保存KM图: {img_path}")

    # 保存相似序号明细
    sim_df = pd.DataFrame(sim_list, columns=['相似序号', '相似度'])
    sim_detail_path = out_dir / f"相似序号明细_{target_part_num}_{target_serial}.xlsx"
    sim_df.to_excel(sim_detail_path, index=False)
    print(f"✅ 已保存相似序号明细: {sim_detail_path}")

    return out_dir


if __name__ == "__main__":
    try:
        # 使用列表格式录入：(target_part, target_serial, sim_input_list)
        # 示例：TASKS = [("1706903", "SN001", ["SN002","SN003"]) ]
        # 任务列表定义见文件顶部配置区域

        if not TASKS:
            print("⚠️ 未配置 TASKS。请在脚本底部填写 TASKS 列表，例如：")
            print("TASKS = [(\"1706903\", \"SN001\", [\"SN002\", \"SN003\"]) ]")
            sys.exit(0)

        for target_part, target_serial, sim_list in TASKS:
            print(f"\n=== 处理: 件号={target_part}, 序号={target_serial}, 相似序号={sim_list} ===")
            build_weighted_km_for_target(
                repair_history_file=str(DEFAULT_REPAIR_HISTORY_FILE),
                target_part_num=str(target_part),
                target_serial=str(target_serial),
                similar_serials=[str(s) for s in sim_list],
            )
        print("\n✅ 全部处理完成")
    except Exception as e:
        print(f"❌ 运行失败: {e}") 