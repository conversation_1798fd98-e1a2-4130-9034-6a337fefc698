# -*- coding: utf-8 -*-
"""
航材采购与库存风险分析器 (V2.5 - 蒙特卡洛安全库存算法)
Procurement and Inventory Risk Analyzer (V2.5 - Monte Carlo Safety Stock Algorithm)

功能：
1. 自动调用并执行 `part_based_tsr_confidence_analyzer` 以生成最新的TSR预测。
2. 自动获取前一步的输出目录，实现无缝数据衔接。
3. 结合当前装机、在修和库存数据，并自动清理列名空格。
4. 基于预测的剩余寿命和置信度，评估各序号件的故障风险。
5. 新增"本TAT周期前剩余TSR"及预警标签和故障发生"基地"的判断逻辑。
6. 按件号汇总，计算周期内的缺件风险、保障率及所需安全库存。
7. 使用蒙特卡洛方法(10000次模拟)重新设计安全库存算法，考虑KM生存曲线统计分布。
8. 生成格式优化的Excel报告，包含"坏件细节"、"各基地缺件详情"工作表和百分比格式。
"""
import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path
import math
import sys
import datetime

# --- 动态导入上游分析模块 ---
# 假设此脚本与 part_based_tsr_confidence_analyzer.py 在同一项目结构中
# 请根据需要调整路径
try:
    # 假设上游脚本在可访问的路径中
    from part_based_tsr_confidence_analyzer import PartBasedTSRConfidenceAnalyzer
except ImportError:
    print("错误: 无法导入 'PartBasedTSRConfidenceAnalyzer'。")
    print("请确保 part_based_tsr_confidence_analyzer.py 文件位于Python的搜索路径中。")
    # 尝试从特定路径添加，请根据您的项目结构修改
    # sys.path.append(r"C:\path\to\your\scripts")
    # from part_based_tsr_confidence_analyzer import PartBasedTSRConfidenceAnalyzer
    sys.exit(1)

# --- 动态导入综合TSR分析模块 ---
try:
    from comprehensive_tsr_analyzer import ComprehensiveTSRAnalyzer, BusinessRuleManager
except ImportError:
    print("错误: 无法导入 'ComprehensiveTSRAnalyzer'。")
    print("请确保 comprehensive_tsr_analyzer.py 文件位于Python的搜索路径中。")
    sys.exit(1)


# --- 全局配置参数 (Global Configuration Parameters) ---

# 1. 文件路径配置 (File Paths) - 默认路径，可通过主函数参数覆盖
BASE_DATA_PATH = Path(r"/home/<USER>/mu_air_python/mu_air_python/model")
AIRCRAFT_DATA_PATH = BASE_DATA_PATH / "飞机数据"
DEFAULT_REPAIR_HISTORY_FILE = r"飞机数据/送修数据_新增平均TSR.xlsx"
DEFAULT_CURRENT_INSTALL_FILE = AIRCRAFT_DATA_PATH / "当前装机件号详情.xlsx"
DEFAULT_INVENTORY_FILE = AIRCRAFT_DATA_PATH / "库存情况.xlsx"
DEFAULT_REPAIR_IN_PROGRESS_FILE = AIRCRAFT_DATA_PATH / "在修零部件数据.xlsx"

# 2. 风险评估参数 (Risk Assessment Parameters)
CONFIDENCE_THRESHOLD = 0.75  # 判定为故障的置信度阈值 (Confidence threshold for fault determination)
TARGET_SUPPORT_RATE = 0.93   # 航材目标保障率 (Target material support rate)
TSR_PREDICTION_PERCENTILE = 0.50  # TSR预测分位数，默认50%分位数 (TSR prediction percentile, default 50%)
SURVIVAL_CURVE_DATA_THRESHOLD = 10  # 生存曲线数据量阈值，小于此值使用件号级KM曲线 (Survival curve data threshold for using part-level KM curve)

# 3. 蒙特卡洛安全库存计算参数 (Monte Carlo Safety Stock Calculation Parameters)
MONTE_CARLO_SIMULATIONS = 50000  # 蒙特卡洛模拟次数
SAFETY_STOCK_CONFIDENCE_LEVEL = 0.95  # 安全库存置信水平
FAULTY_CONFIDENCE_THRESHOLD = 0.725 # 故障判定置信度阈值

# 4. 模型版本参数 (Model Version Parameters)
MODEL_VERSION_RISK_ANALYSIS = "1.0"  # 风险分析模型版本
MODEL_VERSION_KM_SURVIVAL = "1.0"  # KM生存曲线模型版本
MODEL_VERSION_SAFETY_STOCK = "1.0"  # 蒙特卡洛安全库存模型版本

# 5. 外部预计算模式配置 (External Precomputed Mode)
ENABLE_UPSTREAM_PROCESSING = False  # 为 False 时跳过上游数据加载与序号级风险计算
EXTERNAL_PRECOMPUTED_FILE = r"飞机数据/按件号TSR预测与置信度分析结果_KM生存曲线.xlsx"


class ProcurementRiskAnalyzer:
    """
    一个用于分析航材采购和库存风险的类。
    A class to analyze procurement and inventory risks for aviation materials.
    """

    def __init__(self, confidence_threshold, target_support_rate, tsr_percentile=TSR_PREDICTION_PERCENTILE, 
                 survival_curve_data_threshold=SURVIVAL_CURVE_DATA_THRESHOLD,
                 current_install_file=DEFAULT_CURRENT_INSTALL_FILE,
                 inventory_file=DEFAULT_INVENTORY_FILE,
                 repair_in_progress_file=DEFAULT_REPAIR_IN_PROGRESS_FILE,
                 enable_upstream_processing: bool = ENABLE_UPSTREAM_PROCESSING,
                 external_precomputed_file: str = EXTERNAL_PRECOMPUTED_FILE):
        """
        Initializes the analyzer.
        """
        self.confidence_threshold = confidence_threshold
        self.target_support_rate = target_support_rate
        self.tsr_percentile = tsr_percentile
        self.survival_curve_data_threshold = survival_curve_data_threshold
        self.output_dir = None
        self.final_report_path = None
        self.survival_data = None  # 存储KM生存曲线数据
        
        # 数据文件路径
        self.current_install_file = Path(current_install_file)
        self.inventory_file = Path(inventory_file)
        self.repair_in_progress_file = Path(repair_in_progress_file)

        # 新增：外部模式控制
        self.enable_upstream_processing = enable_upstream_processing
        self.external_precomputed_file = external_precomputed_file
        
        print("\n--- 航材采购与库存风险分析已启动 ---")
        print(f"风险分析模型版本: {MODEL_VERSION_RISK_ANALYSIS}")
        print(f"KM生存曲线模型版本: {MODEL_VERSION_KM_SURVIVAL}")
        print(f"蒙特卡洛安全库存模型版本: {MODEL_VERSION_SAFETY_STOCK}")
        print(f"置信度阈值 (Confidence Threshold): {self.confidence_threshold}")
        print(f"目标保障率 (Target Support Rate): {self.target_support_rate}")
        print(f"TSR预测分位数 (TSR Prediction Percentile): {self.tsr_percentile:.1%}")
        print(f"生存曲线数据量阈值 (Survival Curve Data Threshold): {self.survival_curve_data_threshold}")
        print(f"蒙特卡洛模拟次数: {MONTE_CARLO_SIMULATIONS}")
        print(f"安全库存置信水平: {SAFETY_STOCK_CONFIDENCE_LEVEL:.1%} ({SAFETY_STOCK_CONFIDENCE_LEVEL*100:.0f}%分位数)")
        print(f"是否启用上游数据加载与风险计算: {self.enable_upstream_processing}")
        if not self.enable_upstream_processing:
            print(f"外部预计算结果文件: {self.external_precomputed_file}")

    def _load_data(self):
        """
        加载所有需要的数据文件，并清理列名。
        Loads all necessary data files and cleans column names.
        """
        print("\n2. 正在加载数据文件...")
        try:
            # 1. 加载TSR预测与置信度结果
            tsr_result_file_pattern = self.output_dir / "按件号TSR预测与置信度分析结果_V*.xlsx"
            tsr_result_files = list(tsr_result_file_pattern.parent.glob(tsr_result_file_pattern.name))
            
            if not tsr_result_files:
                raise FileNotFoundError(f"在目录 {self.output_dir} 中未找到TSR分析结果Excel文件。")
            
            tsr_result_file_path = tsr_result_files[0]
            df_tsr = pd.read_excel(tsr_result_file_path, sheet_name="TSR预测与置信度")
            df_tsr.columns = df_tsr.columns.str.strip() # 清理列名空格
            print(f"成功加载TSR预测结果: {tsr_result_file_path.name} ({len(df_tsr)} 行)")

            # 2. 加载当前装机件号详情
            df_install = pd.read_excel(self.current_install_file)
            df_install.columns = df_install.columns.str.strip() # 清理列名空格
            print(f"成功加载当前装机详情: {self.current_install_file.name} ({len(df_install)} 行)")

            # 3. 加载库存情况
            df_inventory = pd.read_excel(self.inventory_file)
            df_inventory.columns = df_inventory.columns.str.strip() # 清理列名空格
            print(f"成功加载库存情况: {self.inventory_file.name} ({len(df_inventory)} 行)")

            # 4. 加载在修零部件数据
            df_repair = pd.read_excel(self.repair_in_progress_file)
            df_repair.columns = df_repair.columns.str.strip() # 清理列名空格
            print(f"成功加载在修数据: {self.repair_in_progress_file.name} ({len(df_repair)} 行)")

            # 5. 加载survival_data（KM生存曲线数据）
            df_survival_data = self._load_survival_data(df_tsr)

            return df_tsr, df_install, df_inventory, df_repair, df_survival_data

        except FileNotFoundError as e:
            print(f"数据加载错误: 文件未找到 - {e}")
            raise
        except Exception as e:
            print(f"数据加载时发生未知错误: {e}")
            raise

    def _load_survival_data(self, df_tsr):
        """
        加载所有序号的KM生存曲线数据并整合到一个表格中
        
        数据格式：
        - 件号：从df_tsr中获取
        - 序号：从df_tsr中获取  
        - timeline：KM生存曲线的时间点
        - survival_probability：对应时间点的生存概率
        - ci_lower_0.95：95%置信区间下界
        - ci_upper_0.95：95%置信区间上界
        """
        print("\n2.5. 正在加载KM生存曲线数据...")
        
        # 查找survival_data目录
        survival_data_dir = self.output_dir / "survival_data"
        if not survival_data_dir.exists():
            print("⚠️ 未找到survival_data目录，将创建空的生存曲线数据表")
            return pd.DataFrame(columns=['件号', '序号', 'timeline', 'survival_probability', 'ci_lower_0.95', 'ci_upper_0.95'])
        
        all_survival_data = []
        successful_loads = 0
        failed_loads = 0
        
        # 遍历每个序号，加载对应的KM生存曲线数据
        for _, row in df_tsr.iterrows():
            part_num = row['件号']
            serial = row['序号']
            
            # 构建KM生存曲线文件路径
            km_file_path = survival_data_dir / f"{serial}_km_curve.xlsx"
            
            try:
                if km_file_path.exists():
                    # 读取KM生存曲线数据
                    km_data = pd.read_excel(km_file_path)
                    
                    # 为每行数据添加件号和序号信息
                    km_data['件号'] = part_num
                    km_data['序号'] = serial
                    
                    # 处理置信区间列名的兼容性
                    if 'ci_lower_0.95' not in km_data.columns:
                        if 'KM_estimate_lower_0.95' in km_data.columns:
                            km_data['ci_lower_0.95'] = km_data['KM_estimate_lower_0.95']
                        else:
                            km_data['ci_lower_0.95'] = None
                    
                    if 'ci_upper_0.95' not in km_data.columns:
                        if 'KM_estimate_upper_0.95' in km_data.columns:
                            km_data['ci_upper_0.95'] = km_data['KM_estimate_upper_0.95']
                        else:
                            km_data['ci_upper_0.95'] = None
                    
                    # 确保所有必需的列都存在
                    required_columns = ['件号', '序号', 'timeline', 'survival_probability', 'ci_lower_0.95', 'ci_upper_0.95']
                    for col in required_columns:
                        if col not in km_data.columns:
                            km_data[col] = None
                    
                    # 确保列的顺序符合要求
                    km_data = km_data[required_columns]
                    
                    all_survival_data.append(km_data)
                    successful_loads += 1
                else:
                    # 如果文件不存在，创建一个空记录
                    empty_record = pd.DataFrame({
                        '件号': [part_num],
                        '序号': [serial],
                        'timeline': [None],
                        'survival_probability': [None],
                        'ci_lower_0.95': [None],
                        'ci_upper_0.95': [None]
                    })
                    all_survival_data.append(empty_record)
                    failed_loads += 1
                    
            except Exception as e:
                print(f"⚠️ 读取序号 {serial} 的KM生存曲线数据失败: {e}")
                # 创建一个错误记录
                error_record = pd.DataFrame({
                    '件号': [part_num],
                    '序号': [serial],
                    'timeline': [None],
                    'survival_probability': [None],
                    'ci_lower_0.95': [None],
                    'ci_upper_0.95': [None]
                })
                all_survival_data.append(error_record)
                failed_loads += 1
        
        # 合并所有数据
        if all_survival_data:
            df_survival_combined = pd.concat(all_survival_data, ignore_index=True)
        else:
            df_survival_combined = pd.DataFrame(columns=['件号', '序号', 'timeline', 'survival_probability', 'ci_lower_0.95', 'ci_upper_0.95'])
        
        print(f"KM生存曲线数据加载完成: 成功{successful_loads}个，失败{failed_loads}个")
        print(f"总数据点数: {len(df_survival_combined)}")
        
        # 存储到实例变量中供后续使用
        self.survival_data = df_survival_combined
        
        return df_survival_combined

    def _get_survival_probability_at_tsr(self, serial, tsr_value):
        """
        从KM生存曲线中获取指定TSR的生存概率和95%置信区间
        
        Returns:
            tuple: (survival_probability, ci_lower, ci_upper) 或 (None, None, None)
        """
        if self.survival_data is None:
            return None, None, None
        
        # 获取该序号的KM生存曲线数据
        serial_data = self.survival_data[
            (self.survival_data['序号'] == serial) & 
            (self.survival_data['timeline'].notna())
        ].copy()
        
        if len(serial_data) == 0:
            return None, None, None
        
        # 按timeline排序
        serial_data = serial_data.sort_values('timeline')
        
        # 找到tsr_value对应的生存概率
        timelines = serial_data['timeline'].values
        survival_probs = serial_data['survival_probability'].values
        ci_lowers = serial_data['ci_lower_0.95'].values
        ci_uppers = serial_data['ci_upper_0.95'].values
        
        # 使用插值法获取精确的生存概率
        if tsr_value <= timelines[0]:
            # 如果TSR小于最小时间点，使用第一个点的值
            return survival_probs[0], ci_lowers[0], ci_uppers[0]
        elif tsr_value >= timelines[-1]:
            # 如果TSR大于最大时间点，使用最后一个点的值
            return survival_probs[-1], ci_lowers[-1], ci_uppers[-1]
        else:
            # 线性插值
            survival_prob = np.interp(tsr_value, timelines, survival_probs)
            ci_lower = np.interp(tsr_value, timelines, ci_lowers) if not np.isnan(ci_lowers).all() else survival_prob
            ci_upper = np.interp(tsr_value, timelines, ci_uppers) if not np.isnan(ci_uppers).all() else survival_prob
            return survival_prob, ci_lower, ci_upper

    def _precompute_survival_data(self, df_risk):
        """
        预计算所有序号的生存概率数据，避免重复查询
        """
        print("  正在预计算生存概率数据...")
        survival_cache = {}
        
        for _, row in df_risk.iterrows():
            serial = row['序号']
            predicted_tsr = row['预测TSR']
            
            if pd.isna(predicted_tsr) or predicted_tsr <= 0:
                continue
                
            # 获取生存概率及置信区间
            survival_prob, ci_lower, ci_upper = self._get_survival_probability_at_tsr(serial, predicted_tsr)
            
            if survival_prob is not None:
                # 预处理边界值
                if ci_lower is not None and ci_upper is not None and not np.isnan(ci_lower) and not np.isnan(ci_upper):
                    left_bound = max(0, ci_lower)
                    center_bound = max(0, min(1, survival_prob))
                    right_bound = min(1, ci_upper)
                    
                    # 检查是否有有效的三角分布
                    if abs(right_bound - left_bound) > 1e-10:
                        center_bound = max(left_bound, min(center_bound, right_bound))
                        survival_cache[serial] = {
                            'type': 'triangular',
                            'left': left_bound,
                            'center': center_bound,
                            'right': right_bound
                        }
                    else:
                        survival_cache[serial] = {
                            'type': 'fixed',
                            'value': center_bound
                        }
                else:
                    survival_cache[serial] = {
                        'type': 'fixed',
                        'value': survival_prob
                    }
        
        print(f"  预计算完成，缓存 {len(survival_cache)} 个序号的生存概率数据")
        return survival_cache

    def _monte_carlo_fault_simulation_optimized(self, df_risk):
        """
        优化版蒙特卡洛方法模拟各件号的坏件期望数量
        
        优化策略：
        1. 预计算生存概率数据，避免重复查询
        2. 向量化计算，减少循环次数
        3. 批量处理，提升效率
        """
        print("\n3.5. 开始蒙特卡洛故障模拟（优化版）...")
        print(f"模拟次数: {MONTE_CARLO_SIMULATIONS}")
        print(f"故障判定置信度阈值: {FAULTY_CONFIDENCE_THRESHOLD}")
        
        # 预计算生存概率数据
        survival_cache = self._precompute_survival_data(df_risk)
        
        # 准备批量计算数据
        valid_serials = []
        for _, row in df_risk.iterrows():
            serial = row['序号']
            predicted_tsr = row['预测TSR']
            # 外部模式可能无“本TAT周期后剩余TSR”，因此条件不同
            if '本TAT周期后剩余TSR' in df_risk.columns and self.enable_upstream_processing:
                remaining_tsr = row['本TAT周期后剩余TSR']
            else:
                remaining_tsr = None  # 在外部模式下不作为过滤条件
            part_num = row['件号']
            
            condition_ok = (not pd.isna(predicted_tsr) and predicted_tsr > 0 and serial in survival_cache)
            if self.enable_upstream_processing:
                condition_ok = condition_ok and (remaining_tsr is not None and remaining_tsr < 0)
            
            if condition_ok:
                valid_serials.append({
                    'serial': serial,
                    'part_num': part_num,
                    'remaining_tsr': remaining_tsr if remaining_tsr is not None else -1,
                    'survival_params': survival_cache.get(serial)
                })
        
        print(f"  有效序号数量: {len(valid_serials)}")
        
        if len(valid_serials) == 0:
            print("  无有效序号，跳过蒙特卡洛模拟")
            return {}
        
        # 按件号分组
        part_numbers = df_risk['件号'].unique()
        monte_carlo_results = {}
        
        for part_num in part_numbers:
            print(f"正在模拟件号: {part_num}")
            
            # 获取该件号的有效序号
            part_valid_serials = [s for s in valid_serials if s['part_num'] == part_num]
            
            if len(part_valid_serials) == 0:
                # 该件号无有效序号，设置为0
                monte_carlo_results[part_num] = {
                    'expected_faulty': 0.0,
                    'expected_faulty_rounded': 0,
                    'std_faulty': 0.0,
                    'min_faulty': 0,
                    'max_faulty': 0,
                    'p5': 0, 'p10': 0, 'p25': 0, 'p50': 0,
                    'p75': 0, 'p80': 0, 'p90': 0, 'p95': 0,
                    'simulation_counts': np.zeros(MONTE_CARLO_SIMULATIONS)
                }
                continue
            
            # 向量化蒙特卡洛模拟
            num_serials = len(part_valid_serials)
            
            # 预生成所有随机数（向量化）
            print(f"  生成 {MONTE_CARLO_SIMULATIONS} x {num_serials} 随机样本...")
            
            # 为每个序号生成对应的随机样本
            all_samples = np.zeros((MONTE_CARLO_SIMULATIONS, num_serials))
            
            for i, serial_info in enumerate(part_valid_serials):
                survival_params = serial_info['survival_params']
                
                if survival_params['type'] == 'triangular':
                    # 批量生成三角分布样本
                    samples = np.random.triangular(
                        survival_params['left'],
                        survival_params['center'], 
                        survival_params['right'],
                        size=MONTE_CARLO_SIMULATIONS
                    )
                else:
                    # 固定值
                    samples = np.full(MONTE_CARLO_SIMULATIONS, survival_params['value'])
                
                all_samples[:, i] = samples
            
            # 向量化计算置信度和故障判定
            print(f"  执行向量化故障判定...")
            
            # 计算失效概率（置信度）
            confidences = 1 - all_samples  # shape: (MONTE_CARLO_SIMULATIONS, num_serials)
            
            # 故障判定：置信度 > 阈值
            faults = confidences > FAULTY_CONFIDENCE_THRESHOLD  # shape: (MONTE_CARLO_SIMULATIONS, num_serials)
            
            # 统计每次模拟的坏件总数
            simulation_faulty_counts = np.sum(faults, axis=1)  # shape: (MONTE_CARLO_SIMULATIONS,)
            
            # 计算统计信息
            expected_faulty = np.mean(simulation_faulty_counts)
            expected_faulty_rounded = int(np.ceil(expected_faulty))
            
            # 计算各种分位数，包括动态安全库存分位数
            safety_percentile = SAFETY_STOCK_CONFIDENCE_LEVEL * 100  # 转换为百分位数
            percentile_list = [5, 10, 25, 50, 75, 80, 90, 95]
            if safety_percentile not in percentile_list:
                percentile_list.append(safety_percentile)
                percentile_list.sort()
            
            percentiles = np.percentile(simulation_faulty_counts, percentile_list)
            
            # 找到安全库存分位数在数组中的索引
            safety_idx = percentile_list.index(safety_percentile)
            
            monte_carlo_results[part_num] = {
                'expected_faulty': expected_faulty,
                'expected_faulty_rounded': expected_faulty_rounded,
                'std_faulty': np.std(simulation_faulty_counts),
                'min_faulty': np.min(simulation_faulty_counts),
                'max_faulty': np.max(simulation_faulty_counts),
                'p5': percentiles[percentile_list.index(5)],
                'p10': percentiles[percentile_list.index(10)], 
                'p25': percentiles[percentile_list.index(25)],
                'p50': percentiles[percentile_list.index(50)],
                'p75': percentiles[percentile_list.index(75)],
                'p80': percentiles[percentile_list.index(80)],  # 保留80%分位数用于比较
                'p90': percentiles[percentile_list.index(90)],
                'p95': percentiles[percentile_list.index(95)],
                'p_safety': percentiles[safety_idx],  # 动态安全库存分位数
                'safety_percentile': safety_percentile,  # 记录使用的分位数
                'simulation_counts': simulation_faulty_counts
            }
            
            # print(f"  件号 {part_num}: 期望坏件数={expected_faulty:.2f}, round up={expected_faulty_rounded}, {safety_percentile:.0f}%分位数={percentiles[safety_idx]:.1f}")
        
        # print(f"✅ 优化版蒙特卡洛故障模拟完成，共处理 {len(part_numbers)} 个件号")
        # print(f"   安全库存计算使用 {SAFETY_STOCK_CONFIDENCE_LEVEL*100:.0f}% 分位数")
        
        return monte_carlo_results

    def _monte_carlo_fault_simulation(self, df_risk):
        """
        使用优化版蒙特卡洛方法模拟各件号的坏件期望数量
        """
        return self._monte_carlo_fault_simulation_optimized(df_risk)

    def _calculate_fault_risk(self, df_tsr, df_install):
        """
        合并数据并计算故障风险。
        Merges data and calculates fault risk.
        """
        print("\n3. 正在合并数据并计算故障风险...")
        
        df_merged = pd.merge(
            df_tsr,
            df_install,
            on=['件号', '序号'],
            how='left'
        )
        print(f"数据合并完成，生成 {len(df_merged)} 条记录。")

        # --- 健壮性检查：确保所有需要的列都存在，不存在则填充默认值 ---
        required_cols = {
            '预测TSR': 0, '已使用时长': 0, 'TAT周期内的飞行时长': 0, '置信度': 0,
            '排班结束前飞行时长': 0, '执管基地': '未知', '过夜基地': '未知', '机号': '未知'
        }
        for col, default_value in required_cols.items():
            if col not in df_merged.columns:
                print(f"警告: 合并后的数据中缺少 '{col}' 列，将使用默认值 '{default_value}' 填充。")
                df_merged[col] = default_value
        
        # 对数值列填充NaN
        for col in ['已使用时长', 'TAT周期内的飞行时长', '置信度', '排班结束前飞行时长']:
             df_merged[col].fillna(0, inplace=True)

        # --- 新增功能 1: 计算"本TAT周期前剩余TSR"和"tag" ---
        df_merged['本TAT周期前剩余TSR'] = df_merged['预测TSR'] - df_merged['已使用时长']
        df_merged['tag'] = np.where(df_merged['本TAT周期前剩余TSR'] < 0, "预测TSR小于已运行TSR", "")
        print("新增功能: '本TAT周期前剩余TSR' 和 'tag' 列计算完成。")

        # --- 现有功能: 计算"本TAT周期后剩余TSR"和"是否故障" ---
        df_merged['本TAT周期后剩余TSR'] = df_merged['预测TSR'] - df_merged['已使用时长'] - df_merged['TAT周期内的飞行时长']
        conditions = (df_merged['本TAT周期后剩余TSR'] < 0) & (df_merged['置信度'] > self.confidence_threshold)
        df_merged['是否故障'] = np.where(conditions, 1, 0)
        faulty_count = df_merged['是否故障'].sum()
        print(f"故障风险计算完成。预测故障数量: {faulty_count} 件。")

        # --- 新增功能 2: 判断故障发生"基地" ---
        # 仅对"是否故障"为1的行进行计算
        df_merged['排班结束TSR'] = df_merged['预测TSR'] - df_merged['已使用时长'] - df_merged['排班结束前飞行时长']
        
        # 初始化"基地"列为空字符串
        df_merged['基地'] = ''
        
        # 定义基地判断条件
        fault_mask = df_merged['是否故障'] == 1
        base_condition = df_merged['排班结束TSR'] < 0
        
        df_merged.loc[fault_mask, '基地'] = np.where(base_condition[fault_mask], df_merged.loc[fault_mask, '执管基地'], df_merged.loc[fault_mask, '过夜基地'])
        print("新增功能: 故障发生'基地'判断逻辑完成。")
        
        return df_merged

    def _calculate_statistics(self, df_risk, df_inventory, df_repair):
        """
        按件号进行统计计算，使用蒙特卡洛方法重新设计安全库存算法。
        Performs statistical calculations grouped by part number using Monte Carlo method for safety stock.
        """
        print("\n4. 正在按件号进行统计分析...")
        
        # 执行蒙特卡洛故障模拟
        monte_carlo_results = self._monte_carlo_fault_simulation(df_risk)
        
        # 传统方式计算（用于对比）
        df_shortage = df_risk.groupby('件号')['是否故障'].sum().reset_index()
        df_shortage.rename(columns={'是否故障': '预测周期内缺件数量'}, inplace=True)
        # print("a) '预测周期内缺件数量' 计算完成。")
        
        # 添加蒙特卡洛结果
        monte_carlo_data = []
        for part_num, mc_result in monte_carlo_results.items():
            monte_carlo_data.append({
                '件号': part_num,
                '蒙特卡洛期望坏件数': mc_result['expected_faulty'],
                '蒙特卡洛期望坏件数_整数': mc_result['expected_faulty_rounded'],
                '蒙特卡洛坏件数标准差': mc_result['std_faulty'],
                '蒙特卡洛坏件数_80分位': mc_result['p80'],
                '蒙特卡洛坏件数_90分位': mc_result['p90'],
                '蒙特卡洛坏件数_95分位': mc_result['p95'],
                '蒙特卡洛坏件数_安全库存分位': mc_result['p_safety'],  # 动态安全库存分位数
                '蒙特卡洛坏件数_最小值': mc_result['min_faulty'],
                '蒙特卡洛坏件数_最大值': mc_result['max_faulty']
            })
        
        df_monte_carlo = pd.DataFrame(monte_carlo_data)
        df_shortage = pd.merge(df_shortage, df_monte_carlo, on='件号', how='left')
        # print("a2) '蒙特卡洛坏件期望计算' 完成。")

        repair_counts = df_repair.groupby('件号').size().reset_index(name='件号返修数')
        df_stats = pd.merge(df_shortage, repair_counts, on='件号', how='left')
        df_stats['件号返修数'].fillna(0, inplace=True)
        df_stats['件号返修数'] = df_stats['件号返修数'].astype(int)
        
        # 使用蒙特卡洛结果计算偏差
        df_stats['预估周期内返修与故障偏差量'] = df_stats['蒙特卡洛期望坏件数_整数'] - df_stats['件号返修数']
        # print("b) '预估周期内返修与故障偏差量' 计算完成。")

        def calc_monte_carlo_repair_support_rate(row):
            shortage = row['蒙特卡洛期望坏件数_整数']
            repair = row['件号返修数']
            if shortage == 0:
                return 1.0
            return min(shortage, repair) / shortage
        
        df_stats['预估周期内返修航材保障率'] = df_stats.apply(calc_monte_carlo_repair_support_rate, axis=1)
        # print("c) '预估周期内返修航材保障率' 计算完成。")

        inventory_available = df_inventory[['件号', '总计']].copy()
        inventory_available.rename(columns={'总计': '当前可用库自有器材数量'}, inplace=True)
        df_stats = pd.merge(df_stats, inventory_available, on='件号', how='left')
        df_stats['当前可用库自有器材数量'].fillna(0, inplace=True)
        df_stats['当前可用库自有器材数量'] = df_stats['当前可用库自有器材数量'].astype(int)
        # print("d) '当前可用库自有器材数量' 提取完成。")

        def calc_monte_carlo_own_support_rate(row):
            shortage = row['蒙特卡洛期望坏件数_整数']
            repair = row['件号返修数']
            inventory = row['当前可用库自有器材数量']
            if shortage == 0:
                return 1.0
            return min(shortage, repair + inventory) / shortage

        df_stats['预估周期内自有航材保障率'] = df_stats.apply(calc_monte_carlo_own_support_rate, axis=1)
        # print("e) '预估周期内自有航材保障率' 计算完成。")

        df_stats['航材目标保障率'] = self.target_support_rate
        # print(f"f) '航材目标保障率' 设定为: {self.target_support_rate}")

        def calc_monte_carlo_safety_stock(row):
            """
            基于蒙特卡洛模拟结果计算安全库存
            
            使用SAFETY_STOCK_CONFIDENCE_LEVEL参数对应的分位数坏件需求和返修供给来计算安全库存
            安全库存可以超过现有库存，超出部分为外借数目
            """
            target_rate = row['航材目标保障率']
            # 使用动态安全库存分位数作为需求（对应SAFETY_STOCK_CONFIDENCE_LEVEL）
            mc_demand_safety = row['蒙特卡洛坏件数_安全库存分位']
            repair_supply = row['件号返修数']
            available_inventory = row['当前可用库自有器材数量']
            
            # 如果安全库存分位数需求为0，则无需安全库存
            if mc_demand_safety == 0:
                return {'safety_stock': 0, 'borrow_amount': 0, 'safety_demand': 0}
            
            # 计算为达到目标保障率所需的总供给量
            required_total_supply = target_rate * mc_demand_safety
            
            # 计算需要从库存中预留的数量（最终安全库存）
            required_inventory_reserve = required_total_supply - repair_supply
            
            # 最终安全库存可以超过当前可用库存
            safety_stock = max(0, required_inventory_reserve)
            safety_stock_rounded = int(np.ceil(safety_stock))
            
            # 计算外借数目（超出现有库存的部分）
            borrow_amount = max(0, safety_stock_rounded - available_inventory)
            
            return {'safety_stock': safety_stock_rounded, 'borrow_amount': borrow_amount, 'safety_demand': int(mc_demand_safety)}

        # 计算安全库存和外借数目
        safety_stock_results = df_stats.apply(calc_monte_carlo_safety_stock, axis=1)
        df_stats['安全库存计算的缺件数'] = safety_stock_results.apply(lambda x: x['safety_demand'])
        # 新逻辑：达成保障率需预留安全库存数量 = max(安全库存计算的缺件数 - 件号返修数, 0)
        df_stats['达成保障率需预留安全库存数量'] = np.maximum(df_stats['安全库存计算的缺件数'] - df_stats['件号返修数'], 0)
        df_stats['需外借数目'] = safety_stock_results.apply(lambda x: x['borrow_amount'])
        print("g) '达成保障率需预留安全库存数量' 和 '需外借数目' 计算完成。")
        
        # 计算可外租库存数量（现有库存减去实际占用的安全库存）
        df_stats['蒙特卡洛实际占用安全库存'] = df_stats.apply(
            lambda row: min(row['达成保障率需预留安全库存数量'], row['当前可用库自有器材数量']), axis=1
        )
        df_stats['可用库存可外租数目'] = df_stats['当前可用库自有器材数量'] - df_stats['蒙特卡洛实际占用安全库存']
        df_stats['可用库存可外租数目'] = df_stats['可用库存可外租数目'].apply(lambda x: max(0, x))
        print("h) '可用库存可外租数目' 计算完成。")
        
        # 计算安全库存利用率（基于实际占用的安全库存）
        df_stats['安全库存利用率'] = (df_stats['蒙特卡洛实际占用安全库存'] / 
                                    df_stats['当前可用库自有器材数量']).fillna(0).round(3)
        print("i) '安全库存利用率' 计算完成。")
        
        # 计算外借比例（外借数目占总安全库存需求的比例）
        df_stats['蒙特卡洛外借比例'] = (df_stats['需外借数目'] / 
                                 df_stats['达成保障率需预留安全库存数量']).fillna(0).round(3)
        print("j) '蒙特卡洛外借比例' 计算完成。")
        
        # 删除临时中间计算列
        df_stats = df_stats.drop(['蒙特卡洛实际占用安全库存'], axis=1, errors='ignore')
        print("k) 临时计算列已清理。")

        # 添加蒙特卡洛详细统计信息到调试输出
        mc_debug_file = self.output_dir / "蒙特卡洛模拟详细结果.xlsx"
        with pd.ExcelWriter(mc_debug_file) as writer:
            df_stats.to_excel(writer, sheet_name='汇总结果', index=False)
            
            # 为每个件号保存模拟分布
            for part_num, mc_result in monte_carlo_results.items():
                sim_df = pd.DataFrame({
                    '模拟次数': range(1, MONTE_CARLO_SIMULATIONS + 1),
                    '坏件数量': mc_result['simulation_counts']
                })
                sim_df.to_excel(writer, sheet_name=f'件号_{part_num}_模拟分布', index=False)
        
        print(f"蒙特卡洛详细结果已保存至: {mc_debug_file}")

        return df_stats, df_risk

    def save_results(self, df_stats, df_risk_details, df_survival_data=None):
        """
        将最终结果保存到Excel文件，并增加格式化和坏件细节。
        Saves the final results to an Excel file, with added formatting and faulty parts details.
        """
        print("\n5. 正在保存分析结果...")
        self.final_report_path = self.output_dir / "航材采购与库存风险分析报告_蒙特卡洛版.xlsx"
        
        # --- 格式化百分比列 ---
        df_stats_formatted = df_stats.copy()
        percent_cols = ['预估周期内返修航材保障率', '预估周期内自有航材保障率', '航材目标保障率', 
                       '安全库存利用率', '蒙特卡洛外借比例']
        for col in percent_cols:
            if col in df_stats_formatted.columns:
                df_stats_formatted[col] = df_stats_formatted[col].map('{:.2%}'.format)
        print("报告格式优化：百分比列已格式化。")

        # --- 筛选出坏件细节 ---
        df_faulty_details = df_risk_details[df_risk_details['是否故障'] == 1].copy()
        print(f"报告优化：已筛选出 {len(df_faulty_details)} 条坏件记录。")

        # --- 新增功能: 创建基地缺件汇总表 ---
        df_base_shortage = pd.DataFrame()
        if not df_faulty_details.empty:
            df_base_shortage = df_faulty_details.groupby(['基地', '件号']).agg(
                缺件数量=('序号', 'count'),
                涉及机号=('机号', lambda x: ', '.join(x.astype(str).unique())),
                涉及序号=('序号', lambda x: ', '.join(x.astype(str).unique()))
            ).reset_index()
            print("报告优化：已生成'各基地缺件详情'工作表。")
        else:
            pass
            print("报告信息：无预测故障件，'各基地缺件详情'工作表将为空。")

        # --- 新增功能: 整合KM生存曲线数据 ---
        survival_data_included = False
        if df_survival_data is not None and not df_survival_data.empty:
            # 过滤掉timeline为None的记录（即没有实际KM数据的序号）
            df_survival_filtered = df_survival_data[df_survival_data['timeline'].notna()].copy()
            if not df_survival_filtered.empty:
                survival_data_included = True
                print(f"报告优化：已整合 {len(df_survival_filtered)} 条KM生存曲线数据点。")
            else:
                pass
                print("报告信息：所有序号都没有有效的KM生存曲线数据。")
        else:
            pass
            print("报告信息：未加载到KM生存曲线数据。")

        # --- 为相关表格添加模型版本列 ---
        # 1. 为"件号级风险统计汇总"添加蒙特卡洛安全库存模型版本
        df_stats_with_version = df_stats_formatted.copy()
        df_stats_with_version['蒙特卡洛安全库存模型版本'] = MODEL_VERSION_SAFETY_STOCK
        
        # 2. 为"各序号风险计算详情"添加风险分析模型版本
        df_risk_details_with_version = df_risk_details.copy()
        df_risk_details_with_version['风险分析模型版本'] = MODEL_VERSION_RISK_ANALYSIS
        
        # 3. 为"各基地缺件详情"添加风险分析模型版本
        df_base_shortage_with_version = df_base_shortage.copy()
        if not df_base_shortage_with_version.empty:
            df_base_shortage_with_version['风险分析模型版本'] = MODEL_VERSION_RISK_ANALYSIS
        
        # 4. 为"坏件细节"添加风险分析模型版本
        df_faulty_details_with_version = df_faulty_details.copy()
        if not df_faulty_details_with_version.empty:
            df_faulty_details_with_version['风险分析模型版本'] = MODEL_VERSION_RISK_ANALYSIS
        
        # 5. 为"KM生存曲线数据"添加KM生存曲线模型版本
        if survival_data_included:
            df_survival_with_version = df_survival_filtered.copy()
            df_survival_with_version['KM生存曲线模型版本'] = MODEL_VERSION_KM_SURVIVAL
        else:
            # 空表格也要包含模型版本列
            empty_survival_with_version = pd.DataFrame(columns=[
                '件号', '序号', 'timeline', 'survival_probability', 
                'ci_lower_0.95', 'ci_upper_0.95', 'KM生存曲线模型版本'
            ])

        with pd.ExcelWriter(self.final_report_path) as writer:
            df_stats_with_version.to_excel(writer, sheet_name='件号级风险统计汇总', index=False)
            df_base_shortage_with_version.to_excel(writer, sheet_name='各基地缺件详情', index=False)
            df_faulty_details_with_version.to_excel(writer, sheet_name='坏件细节', index=False)
            df_risk_details_with_version.to_excel(writer, sheet_name='各序号风险计算详情', index=False)
            
            # 新增工作表：KM生存曲线数据整合表
            if survival_data_included:
                df_survival_with_version.to_excel(writer, sheet_name='KM生存曲线数据', index=False)
                print("报告优化：已生成'KM生存曲线数据'工作表（含KM生存曲线模型版本）。")
            else:
                # 即使没有数据也创建空表格，保持报告结构一致
                empty_survival_with_version.to_excel(writer, sheet_name='KM生存曲线数据', index=False)
                print("报告信息：已创建空的'KM生存曲线数据'工作表（含KM生存曲线模型版本）。")
        
        print(f"✅ 风险分析报告已成功保存至: {self.final_report_path}")

    def run(self, tsr_output_dir):
        """
        执行完整的分析流程。
        Executes the complete analysis workflow.
        """
        try:
            self.output_dir = Path(tsr_output_dir)
            print(f"采购风险分析将使用此目录: {self.output_dir}")
            
            if self.enable_upstream_processing:
                # 原有路径：从上游目录加载TSR、KM、装机/库存/在修数据
                df_tsr, df_install, df_inventory, df_repair, df_survival_data = self._load_data()
                df_risk_details = self._calculate_fault_risk(df_tsr, df_install)
            else:
                # 新路径：从外部预计算文件读取TSR与KM总表；仍加载库存/在修数据
                print("使用外部预计算结果：跳过上游数据加载与序号级风险计算")
                pre_path = Path(self.external_precomputed_file)
                if not pre_path.exists():
                    raise FileNotFoundError(f"外部预计算结果文件不存在: {pre_path}")
                # 读取TSR与置信度
                df_tsr = pd.read_excel(pre_path, sheet_name='TSR预测与置信度')
                df_tsr.columns = df_tsr.columns.str.strip()
                # 读取KM生存曲线总表，并转换为内部标准列名
                df_km_total = pd.read_excel(pre_path, sheet_name='KM生存曲线总表')
                df_km_total.columns = df_km_total.columns.str.strip()
                # 列名映射：TSR->timeline、生存置信度->survival_probability
                rename_map = {
                    'TSR': 'timeline',
                    '生存置信度': 'survival_probability',
                    'CI下界0.95': 'ci_lower_0.95',
                    'CI上界0.95': 'ci_upper_0.95'
                }
                for old, new in rename_map.items():
                    if old in df_km_total.columns:
                        df_km_total.rename(columns={old: new}, inplace=True)
                # 确保必要列存在
                for col in ['件号','序号','timeline','survival_probability','ci_lower_0.95','ci_upper_0.95']:
                    if col not in df_km_total.columns:
                        df_km_total[col] = None
                self.survival_data = df_km_total[['件号','序号','timeline','survival_probability','ci_lower_0.95','ci_upper_0.95']].copy()
                df_survival_data = self.survival_data
                print(f"外部KM生存曲线总表加载完成: {len(self.survival_data)} 条数据点")
                
                # 加载当前装机、库存与在修数据
                df_install = pd.read_excel(self.current_install_file)
                df_install.columns = df_install.columns.str.strip()
                print(f"成功加载当前装机详情: {self.current_install_file.name} ({len(df_install)} 行)")
                df_inventory = pd.read_excel(self.inventory_file)
                df_inventory.columns = df_inventory.columns.str.strip()
                print(f"成功加载库存情况: {self.inventory_file.name} ({len(df_inventory)} 行)")
                df_repair = pd.read_excel(self.repair_in_progress_file)
                df_repair.columns = df_repair.columns.str.strip()
                print(f"成功加载在修数据: {self.repair_in_progress_file.name} ({len(df_repair)} 行)")
                
                # 使用原始逻辑计算序号级风险与基地等信息
                df_risk_details = self._calculate_fault_risk(df_tsr, df_install)
            
            df_stats, _ = self._calculate_statistics(df_risk_details, df_inventory, df_repair)
            self.save_results(df_stats, df_risk_details, df_survival_data)
            
            print("\n--- 蒙特卡洛采购风险分析流程成功结束 ---")
            return self.final_report_path
        except (FileNotFoundError, Exception) as e:
            print(f"\n--- 采购风险分析流程因错误中断 ---")
            print(f"错误详情: {e}")
            return None


def run_comprehensive_tsr_analysis(output_directory, repair_history_file=DEFAULT_REPAIR_HISTORY_FILE):
    """
    运行综合TSR分析，包括件号级模型训练和可视化
    """
    print("\n--- 步骤 3: 启动综合TSR模型分析 ---")
    
    try:
        # 使用传入的维修历史文件路径
        input_file_path = repair_history_file
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file_path):
            print(f"⚠️ 输入数据文件不存在: {input_file_path}")
            print("跳过综合TSR分析")
            return False
        
        print(f"📂 使用输入数据文件: {input_file_path}")
        
        # 加载数据
        print("🔄 加载送修数据...")
        raw_df = pd.read_excel(input_file_path)
        print(f"✅ 成功加载 {len(raw_df)} 条记录")
        
        # 初始化分析器
        rule_manager = BusinessRuleManager()
        comprehensive_analyzer = ComprehensiveTSRAnalyzer(rule_manager)
        
        # 数据处理
        print("🔄 处理数据并计算特征...")
        processed_df = comprehensive_analyzer.process_data(raw_df)
        print(f"✅ 数据处理完成，生成 {len(processed_df)} 条处理后记录")
        
        # 运行增强的件号级模型分析
        print("🚀 开始增强的件号级模型分析...")
        enhanced_analysis_results = comprehensive_analyzer.analyze_and_save_part_models_enhanced(
            processed_df, str(output_directory)
        )
        
        if enhanced_analysis_results:
            enhanced_stats = enhanced_analysis_results['overall_statistics']
            print(f"\n🎉 综合TSR模型分析完成!")
            print(f"   ✅ 成功处理件号: {enhanced_stats['successful_parts']}")
            print(f"   ❌ 失败处理件号: {enhanced_stats['failed_parts']}")
            print(f"   📈 成功率: {enhanced_stats['success_rate']:.1%}")
            print(f"   📁 模型分析输出目录: {enhanced_stats['output_directory']}")
            print(f"   🔧 每个件号包含3个模型: 特征增强梯度提升、特征增强随机森林、线性回归基线")
            print(f"   📊 为每个件号生成: 模型对比可视化、特征工程分析、性能对比表格")
            print(f"   💾 模型文件按件号分别保存在子文件夹中")
            return True
        else:
            print("❌ 综合TSR模型分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 综合TSR分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_procurement_analysis_main():
    """
    执行采购风险分析的主函数，以便被其他脚本调用。
    Returns:
        str: 分析结果的输出目录路径，如果失败则返回 None。
    """
    print("\n" + "="*80)
    print("🔧 数据文件路径配置")
    print("="*80)
    
    REPAIR_HISTORY_FILE = DEFAULT_REPAIR_HISTORY_FILE
    print(f"📋 历史维修数据: {REPAIR_HISTORY_FILE}")
    
    CURRENT_INSTALL_FILE = DEFAULT_CURRENT_INSTALL_FILE  
    print(f"✈️ 当前装机详情: {CURRENT_INSTALL_FILE}")
    
    INVENTORY_FILE = DEFAULT_INVENTORY_FILE
    print(f"📦 库存情况: {INVENTORY_FILE}")
    
    REPAIR_IN_PROGRESS_FILE = DEFAULT_REPAIR_IN_PROGRESS_FILE
    print(f"🔧 在修零部件: {REPAIR_IN_PROGRESS_FILE}")
    
    print(f"🧭 外部预计算模式: {ENABLE_UPSTREAM_PROCESSING is False}")
    if not ENABLE_UPSTREAM_PROCESSING:
        print(f"📄 外部汇总文件: {EXTERNAL_PRECOMPUTED_FILE}")
    
    # 当启用上游处理时，校验历史维修数据；否则只校验库存/在修
    data_files = {
        "当前装机详情": CURRENT_INSTALL_FILE,
        "库存情况": INVENTORY_FILE,  
        "在修零部件": REPAIR_IN_PROGRESS_FILE
    }
    if ENABLE_UPSTREAM_PROCESSING:
        data_files = {
            **data_files,
            "历史维修数据": REPAIR_HISTORY_FILE,
        }
    
    missing_files = []
    for name, file_path in data_files.items():
        if not os.path.exists(file_path):
            missing_files.append(f"{name}: {file_path}")
            print(f"❌ {name}文件不存在: {file_path}")
        else:
            print(f"✅ {name}文件存在")
    
    if missing_files:
        print(f"\n⚠️ 警告: 发现 {len(missing_files)} 个文件缺失")
        print("请检查文件路径或联系管理员")
        print("缺失文件列表:")
        for missing in missing_files:
            print(f"  - {missing}")
        print("\n继续运行可能会导致错误...")
        input("按回车键继续...")
    
    print("="*80)
    
    # 分支：是否执行上游TSR与置信度分析
    if ENABLE_UPSTREAM_PROCESSING:
        print("\n--- 步骤 1: 启动TSR与置信度分析 ---")
        tsr_analyzer = PartBasedTSRConfidenceAnalyzer(
            tsr_percentile=TSR_PREDICTION_PERCENTILE,
            survival_curve_data_threshold=SURVIVAL_CURVE_DATA_THRESHOLD,
            repair_history_file=REPAIR_HISTORY_FILE
        )
        tsr_output_file = tsr_analyzer.run_complete_analysis()
    else:
        tsr_analyzer = None
        tsr_output_file = None
        print("\n--- 外部预计算模式：跳过步骤 1（TSR与置信度分析） ---")
    
    # 决定输出目录
    if ENABLE_UPSTREAM_PROCESSING and tsr_output_file and tsr_analyzer and tsr_analyzer.output_dir:
        output_directory = tsr_analyzer.output_dir
    else:
        # 使用预设基目录作为输出目录（外部预计算模式）
        base_dir = Path(BASE_DATA_PATH) / "test" / "安全库存"
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        output_directory = base_dir / timestamp
        os.makedirs(output_directory, exist_ok=True)
    
    print("\n--- 步骤 2: 蒙特卡洛采购风险分析 ---")
    procurement_analyzer = ProcurementRiskAnalyzer(
        confidence_threshold=CONFIDENCE_THRESHOLD,
        target_support_rate=TARGET_SUPPORT_RATE,
        tsr_percentile=TSR_PREDICTION_PERCENTILE,
        survival_curve_data_threshold=SURVIVAL_CURVE_DATA_THRESHOLD,
        current_install_file=CURRENT_INSTALL_FILE,
        inventory_file=INVENTORY_FILE,
        repair_in_progress_file=REPAIR_IN_PROGRESS_FILE,
        enable_upstream_processing=ENABLE_UPSTREAM_PROCESSING,
        external_precomputed_file=EXTERNAL_PRECOMPUTED_FILE
    )
    procurement_result = procurement_analyzer.run(tsr_output_dir=output_directory)
    
    if procurement_result:
        if ENABLE_UPSTREAM_PROCESSING:
            print("\n--- 步骤 3: 启动综合TSR模型分析 ---")
            comprehensive_result = run_comprehensive_tsr_analysis(output_directory, REPAIR_HISTORY_FILE)
            if comprehensive_result:
                print("\n==========================================")
                print("🎉🎉🎉 完整的三步骤分析流程执行完毕 🎉🎉🎉")
                print("==========================================")
                print("✅ 步骤 1: TSR与置信度分析 - 完成")
                print("✅ 步骤 2: 蒙特卡洛采购风险分析 - 完成") 
                print("✅ 步骤 3: 综合TSR模型分析 - 完成")
                print(f"📁 所有结果均已保存在目录: {output_directory}")
                print("==========================================")
                print("\n📋 输出内容包括:")
                print("   🔸 TSR预测与置信度分析结果")
                print("   🔸 KM生存曲线数据")
                print("   🔸 蒙特卡洛采购风险分析报告")
                print("   🔸 件号级模型分析(3个模型/件号)")
                print("   🔸 模型性能对比可视化")
                print("   🔸 特征工程分析图表")
                print("   🔸 特征与TSR相关性分析")
                return str(output_directory)
            else:
                print("\n⚠️⚠️⚠️ 综合TSR模型分析失败 ⚠️⚠️⚠️")
                print("前两步分析已完成，请检查综合分析的错误日志。")
                print(f"前两步结果已保存在目录: {output_directory}")
                return str(output_directory)
        else:
            print("\n==========================================")
            print("🎉 蒙特卡洛采购风险分析执行完毕（外部预计算模式） 🎉")
            print("==========================================")
            print("✅ 步骤 2: 蒙特卡洛采购风险分析 - 完成") 
            print(f"📁 结果保存在目录: {output_directory}")
            print("==========================================")
            return str(output_directory)
    else:
        print("\n⚠️⚠️⚠️ 采购风险分析失败 ⚠️⚠️⚠️")
        print("请检查采购风险分析的错误日志。")
        return None


if __name__ == '__main__':
    run_procurement_analysis_main()
