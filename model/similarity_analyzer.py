# -*- coding: utf-8 -*-
"""
件序号相似度分析脚本
基于part_based_tsr_confidence_analyzer.py开发，专门用于生成相似度分析结果

功能：
1. 读取维修历史数据
2. 按件号分组分析
3. 计算每个序号与其他序号的相似度
4. 输出相似度高于0.7的序号对及其相似度
5. 生成详细的相似度分析报告

输出：
- 相似度分析结果Excel文件
- 按件号分组的相似度统计
- 高相似度序号对明细
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 导入comprehensive_tsr_analyzer模块
sys.path.append(r"C:\Users\<USER>\Desktop\中航材\codes\git_project\air-ml-main")
try:
    from comprehensive_tsr_analyzer import ComprehensiveTSRAnalyzer, BusinessRuleManager
    print("✅ 成功导入comprehensive_tsr_analyzer模块")
except ImportError as e:
    print(f"❌ 导入comprehensive_tsr_analyzer模块失败: {e}")
    sys.exit(1)

# =============================================================================
# 配置参数
# =============================================================================
INPUT_FILE_PATH = r"飞机数据\送修数据_新增平均TSR.xlsx"
OUTPUT_BASE_PATH = r"modeloutput\KM_Curve_similarity"
MODEL_VERSION = "V1.0"

# 相似度分析参数
SIMILARITY_THRESHOLD = 0.7       # 相似度阈值
TOP_SIMILAR_COUNT = 20           # 每个序号保留的最大相似序号数量
SAVE_ALL_SIMILARITY = True       # 是否保存所有相似度结果（包括低于阈值的）
MIN_SERIAL_RECORDS = 2           # 序号最少需要多少条维修记录才参与分析

# 数据清洗规则（与原始脚本保持一致）
DEFAULT_CLEANING_RULES = {
    'exclude_group_abnormal_types': ['件退出'],
    'exclude_single_record_abnormal_types': ['未修好', '合同执行中', '未检测出故障']
}

# 特征权重配置（与原始脚本保持一致）
FEATURE_WEIGHTS = {
    'TSN': 0.3,
    'TSR': 0.1,
    'CSN': 0.1,
    '报价_归一化': 0.3,
    'repair_sequence': 0.2
}

class SimilarityAnalyzer:
    """件序号相似度分析器"""
    
    def __init__(self, similarity_threshold=SIMILARITY_THRESHOLD,
                 top_similar_count=TOP_SIMILAR_COUNT,
                 repair_history_file=INPUT_FILE_PATH,
                 data_cleaning_rules=None):
        self.rule_manager = BusinessRuleManager()
        self.tsr_analyzer = ComprehensiveTSRAnalyzer(self.rule_manager)
        self.output_dir = None
        self.similarity_results = []  # 存储所有相似度结果
        self.part_similarity_stats = {}  # 按件号的相似度统计
        self.debug_log = []
        
        # 超参数
        self.similarity_threshold = similarity_threshold
        self.top_similar_count = top_similar_count
        
        # 数据文件路径
        self.repair_history_file = repair_history_file
        
        # 数据清洗规则
        self.data_cleaning_rules = data_cleaning_rules or DEFAULT_CLEANING_RULES
        
        self.log_debug(f"初始化相似度分析器:")
        self.log_debug(f"  相似度阈值: {self.similarity_threshold}")
        self.log_debug(f"  最大相似序号数: {self.top_similar_count}")
        self.log_debug(f"  维修历史文件: {self.repair_history_file}")
        
    def log_debug(self, message, level="INFO"):
        """记录调试日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.debug_log.append(log_entry)
        
    def create_output_directory(self):
        """创建带时间戳的输出目录"""
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        self.output_dir = os.path.join(OUTPUT_BASE_PATH, f"件序号相似度分析_{timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 创建子文件夹
        self.similarity_dir = os.path.join(self.output_dir, 'similarity_results')
        self.debug_dir = os.path.join(self.output_dir, 'debug_logs')
        self.statistics_dir = os.path.join(self.output_dir, 'statistics')
        
        os.makedirs(self.similarity_dir, exist_ok=True)
        os.makedirs(self.debug_dir, exist_ok=True)
        os.makedirs(self.statistics_dir, exist_ok=True)
        
        self.log_debug(f"输出目录创建完成: {self.output_dir}")
        return self.output_dir

    def normalize_price_data_globally(self, df):
        """对整个数据集的报价进行全局归一化"""
        self.log_debug("开始全局归一化报价数据...")
        
        if '报价' not in df.columns:
            self.log_debug("⚠️ 数据中不包含报价列", "WARNING")
            return df
        
        price_data = df['报价'].dropna()
        if len(price_data) == 0:
            self.log_debug("⚠️ 报价列无有效数据", "WARNING")
            return df
        
        price_min = price_data.min()
        price_max = price_data.max()
        
        if price_max > price_min:
            df['报价_归一化'] = df['报价'].apply(
                lambda x: (x - price_min) / (price_max - price_min) if pd.notna(x) else np.nan
            )
            self.log_debug(f"✅ 全局报价归一化完成: [{price_min:.0f}, {price_max:.0f}] -> [0.0, 1.0]")
        else:
            df['报价_归一化'] = df['报价']
            self.log_debug("⚠️ 报价数据无变异，复制原始数据", "WARNING")
        
        return df

    def calculate_comprehensive_similarity(self, target_data, candidate_data):
        """计算综合相似度（基于多个特征）"""
        try:
            similarities = {}
            
            # 1. TSN相似性
            if 'TSN' in target_data.columns and 'TSN' in candidate_data.columns:
                target_tsn_mean = target_data['TSN'].mean()
                candidate_tsn_mean = candidate_data['TSN'].mean()
                
                if max(target_tsn_mean, candidate_tsn_mean) > 0:
                    tsn_similarity = 1 - abs(target_tsn_mean - candidate_tsn_mean) / max(target_tsn_mean, candidate_tsn_mean)
                else:
                    tsn_similarity = 1.0
                similarities['TSN'] = tsn_similarity
            else:
                similarities['TSN'] = 0.5  # 默认中等相似度
            
            # 2. TSR相似性
            if 'TSR' in target_data.columns and 'TSR' in candidate_data.columns:
                target_tsr_mean = target_data['TSR'].mean()
                candidate_tsr_mean = candidate_data['TSR'].mean()
                
                if max(target_tsr_mean, candidate_tsr_mean) > 0:
                    tsr_similarity = 1 - abs(target_tsr_mean - candidate_tsr_mean) / max(target_tsr_mean, candidate_tsr_mean)
                else:
                    tsr_similarity = 1.0
                similarities['TSR'] = tsr_similarity
            else:
                similarities['TSR'] = 0.5
            
            # 3. CSN相似性
            if 'CSN' in target_data.columns and 'CSN' in candidate_data.columns:
                target_csn_mean = target_data['CSN'].mean()
                candidate_csn_mean = candidate_data['CSN'].mean()
                
                if max(target_csn_mean, candidate_csn_mean) > 0:
                    csn_similarity = 1 - abs(target_csn_mean - candidate_csn_mean) / max(target_csn_mean, candidate_csn_mean)
                else:
                    csn_similarity = 1.0
                similarities['CSN'] = csn_similarity
            else:
                similarities['CSN'] = 0.5
            
            # 4. 维修次数相似性
            target_repair_count = len(target_data)
            candidate_repair_count = len(candidate_data)
            
            if max(target_repair_count, candidate_repair_count) > 0:
                repair_similarity = 1 - abs(target_repair_count - candidate_repair_count) / max(target_repair_count, candidate_repair_count)
            else:
                repair_similarity = 1.0
            similarities['维修次数'] = repair_similarity
            
            # 5. 报价相似性（如果存在归一化报价）
            if '报价_归一化' in target_data.columns and '报价_归一化' in candidate_data.columns:
                target_price_mean = target_data['报价_归一化'].mean()
                candidate_price_mean = candidate_data['报价_归一化'].mean()
                
                if pd.notna(target_price_mean) and pd.notna(candidate_price_mean):
                    price_similarity = 1 - abs(target_price_mean - candidate_price_mean)
                    similarities['报价'] = max(0, price_similarity)
                else:
                    similarities['报价'] = 0.5
            else:
                similarities['报价'] = 0.5
            
            # 6. 维修序列相似性（基于第几次维修的分布）
            if '第几次维修' in target_data.columns and '第几次维修' in candidate_data.columns:
                target_repair_seq = target_data['第几次维修'].value_counts(normalize=True)
                candidate_repair_seq = candidate_data['第几次维修'].value_counts(normalize=True)
                
                # 计算序列分布的相似性
                all_repair_numbers = set(target_repair_seq.index) | set(candidate_repair_seq.index)
                if all_repair_numbers:
                    seq_similarity = 0
                    for repair_num in all_repair_numbers:
                        target_prob = target_repair_seq.get(repair_num, 0)
                        candidate_prob = candidate_repair_seq.get(repair_num, 0)
                        seq_similarity += min(target_prob, candidate_prob)
                    similarities['维修序列'] = seq_similarity
                else:
                    similarities['维修序列'] = 0.5
            else:
                similarities['维修序列'] = 0.5
            
            # 计算加权综合相似度
            weighted_similarity = sum(
                similarities[feature] * FEATURE_WEIGHTS.get(feature, 0.1)
                for feature in similarities.keys()
            )
            
            # 归一化到[0,1]范围
            total_weight = sum(FEATURE_WEIGHTS.get(feature, 0.1) for feature in similarities.keys())
            if total_weight > 0:
                weighted_similarity /= total_weight
            
            return weighted_similarity, similarities
            
        except Exception as e:
            self.log_debug(f"相似性计算异常: {e}", "ERROR")
            return 0.0, {}

    def analyze_serial_similarity(self, part_df, part_num):
        """分析单个件号内所有序号的相似度"""
        self.log_debug(f"开始分析件号 {part_num} 的序号相似度...")
        
        # 获取该件号的所有序号
        serials = part_df['序号'].unique()
        self.log_debug(f"件号 {part_num} 包含 {len(serials)} 个序号")
        
        # 过滤掉维修记录太少的序号
        valid_serials = []
        for serial in serials:
            serial_data = part_df[part_df['序号'] == serial]
            if len(serial_data) >= MIN_SERIAL_RECORDS:
                valid_serials.append(serial)
            else:
                self.log_debug(f"序号 {serial} 维修记录数({len(serial_data)}) < {MIN_SERIAL_RECORDS}，跳过")
        
        self.log_debug(f"有效序号数: {len(valid_serials)}")
        
        if len(valid_serials) < 2:
            self.log_debug(f"件号 {part_num} 有效序号不足2个，跳过相似度分析")
            return
        
        part_similarity_results = []
        part_high_similarity_count = 0
        
        # 计算所有序号对之间的相似度
        for i, target_serial in enumerate(valid_serials):
            target_data = part_df[part_df['序号'] == target_serial].copy()
            
            # 计算与其他序号的相似度
            serial_similarities = []
            
            for candidate_serial in valid_serials:
                if candidate_serial == target_serial:
                    continue
                
                candidate_data = part_df[part_df['序号'] == candidate_serial].copy()
                
                # 计算综合相似度
                overall_similarity, feature_similarities = self.calculate_comprehensive_similarity(
                    target_data, candidate_data
                )
                
                # 记录相似度结果
                similarity_record = {
                    '目标件号': part_num,
                    '目标序号': target_serial,
                    '备选件号': part_num,
                    '备选序号': candidate_serial,
                    '综合相似度': overall_similarity,
                    'TSN相似度': feature_similarities.get('TSN', 0),
                    'TSR相似度': feature_similarities.get('TSR', 0),
                    'CSN相似度': feature_similarities.get('CSN', 0),
                    '维修次数相似度': feature_similarities.get('维修次数', 0),
                    '报价相似度': feature_similarities.get('报价', 0),
                    '维修序列相似度': feature_similarities.get('维修序列', 0),
                    '目标序号维修次数': len(target_data),
                    '备选序号维修次数': len(candidate_data)
                }
                
                # 保存所有相似度结果（如果启用）
                if SAVE_ALL_SIMILARITY:
                    self.similarity_results.append(similarity_record)
                
                # 只保留达到阈值的相似序号
                if overall_similarity >= self.similarity_threshold:
                    serial_similarities.append((candidate_serial, overall_similarity))
                    part_high_similarity_count += 1
                
                # 记录到件号级结果
                part_similarity_results.append(similarity_record)
            
            # 按相似度排序，取前top_n个
            serial_similarities.sort(key=lambda x: x[1], reverse=True)
            top_similar = serial_similarities[:self.top_similar_count]
            
            if top_similar:
                self.log_debug(f"序号 {target_serial}: 找到 {len(top_similar)} 个高相似序号")
                for similar_serial, similarity in top_similar:
                    self.log_debug(f"  - {similar_serial}: {similarity:.3f}")
            else:
                self.log_debug(f"序号 {target_serial}: 无高相似序号")
            
            if i % 10 == 0:
                self.log_debug(f"已处理 {i+1}/{len(valid_serials)} 个序号")
        
        # 统计件号级相似度信息
        self.part_similarity_stats[part_num] = {
            'total_serials': len(valid_serials),
            'total_similarity_pairs': len(part_similarity_results),
            'high_similarity_pairs': part_high_similarity_count,
            'high_similarity_ratio': part_high_similarity_count / len(part_similarity_results) if part_similarity_results else 0,
            'avg_similarity': np.mean([r['综合相似度'] for r in part_similarity_results]) if part_similarity_results else 0
        }
        
        self.log_debug(f"件号 {part_num} 相似度分析完成:")
        self.log_debug(f"  总序号数: {len(valid_serials)}")
        self.log_debug(f"  总相似度对数: {len(part_similarity_results)}")
        self.log_debug(f"  高相似度对数: {part_high_similarity_count}")
        self.log_debug(f"  高相似度比例: {part_high_similarity_count/len(part_similarity_results)*100:.1f}%" if part_similarity_results else "0%")

    def run_similarity_analysis(self):
        """运行完整的相似度分析"""
        self.log_debug("=== 件序号相似度分析系统 ===", "START")
        self.log_debug(f"模型版本: {MODEL_VERSION}")
        self.log_debug(f"输入文件: {self.repair_history_file}")
        self.log_debug(f"相似度阈值: {self.similarity_threshold}")
        
        # 创建输出目录
        self.create_output_directory()
        
        # 读取数据
        try:
            self.log_debug(f"正在读取数据文件: {self.repair_history_file}")
            df = pd.read_excel(self.repair_history_file)
            self.log_debug(f"✅ 成功读取数据: {len(df)} 条记录")
        except Exception as e:
            self.log_debug(f"❌ 读取数据失败: {e}", "ERROR")
            return None
        
        try:
            # 数据清洗
            self.log_debug("开始数据清洗...")
            df_clean = self.apply_input_cleaning_rules(df)
            self.log_debug(f"清洗后数据: {len(df_clean)} / 原始 {len(df)} 条")
            
            # 数据处理
            self.log_debug("开始数据处理...")
            processed_df = self.tsr_analyzer.process_data(df_clean)
            self.log_debug(f"数据处理完成，共 {len(processed_df)} 条记录")
            
            # 报价归一化
            processed_df = self.normalize_price_data_globally(processed_df)
            
            # 按件号分组进行相似度分析
            part_numbers = processed_df['件号'].unique()
            self.log_debug(f"需要分析的件号数: {len(part_numbers)}")
            
            for part_idx, part_num in enumerate(part_numbers, 1):
                self.log_debug(f"分析件号 {part_num} ({part_idx}/{len(part_numbers)})")
                
                # 获取该件号的所有数据
                part_df = processed_df[processed_df['件号'] == part_num].copy()
                
                # 分析该件号内序号的相似度
                self.analyze_serial_similarity(part_df, part_num)
            
            # 保存详细结果
            output_file = self.save_similarity_results()
            
            # 保存简化的四列表格结果
            simplified_output_file = self.save_simplified_similarity_table()
            
            self.log_debug("\n" + "=" * 80)
            self.log_debug("✅ 相似度分析完成！", "SUCCESS")
            self.log_debug("=" * 80)
            self.log_debug(f"📊 总相似度对数: {len(self.similarity_results)}")
            self.log_debug(f"📊 高相似度对数: {len([r for r in self.similarity_results if r['综合相似度'] >= self.similarity_threshold])}")
            self.log_debug(f"📊 详细结果文件: {output_file}")
            self.log_debug(f"📊 简化表格文件: {simplified_output_file}")
            self.log_debug(f"📊 分析目录: {self.output_dir}")
            
            return simplified_output_file
            
        except Exception as e:
            self.log_debug(f"❌ 分析过程中出错: {e}", "ERROR")
            import traceback
            self.log_debug(f"错误详情: {traceback.format_exc()}", "ERROR")
            return None

    def save_similarity_results(self):
        """保存相似度分析结果"""
        self.log_debug("=== 保存相似度分析结果 ===", "STEP")
        
        if not self.similarity_results:
            self.log_debug("❌ 无相似度分析结果", "ERROR")
            return None
        
        # 保存到Excel文件
        output_file = os.path.join(self.output_dir, f"件序号相似度分析结果_{MODEL_VERSION}.xlsx")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 1. 所有相似度结果
            all_results_df = pd.DataFrame(self.similarity_results)
            all_results_df.to_excel(writer, sheet_name='所有相似度结果', index=False)
            self.log_debug(f"✅ 所有相似度结果: {len(all_results_df)} 行")
            
            # 2. 高相似度结果（≥阈值）
            high_similarity_df = all_results_df[all_results_df['综合相似度'] >= self.similarity_threshold].copy()
            high_similarity_df.to_excel(writer, sheet_name='高相似度结果', index=False)
            self.log_debug(f"✅ 高相似度结果: {len(high_similarity_df)} 行")
            
            # 3. 按件号统计
            if self.part_similarity_stats:
                part_stats_df = pd.DataFrame([
                    {
                        '件号': part_num,
                        '总序号数': stats['total_serials'],
                        '总相似度对数': stats['total_similarity_pairs'],
                        '高相似度对数': stats['high_similarity_pairs'],
                        '高相似度比例': f"{stats['high_similarity_ratio']*100:.1f}%",
                        '平均相似度': f"{stats['avg_similarity']:.3f}"
                    }
                    for part_num, stats in self.part_similarity_stats.items()
                ])
                part_stats_df.to_excel(writer, sheet_name='按件号统计', index=False)
                self.log_debug(f"✅ 按件号统计: {len(part_stats_df)} 个件号")
            
            # 4. 相似度分布统计
            similarity_ranges = [
                (0.0, 0.3, '低相似度(0.0-0.3)'),
                (0.3, 0.5, '中低相似度(0.3-0.5)'),
                (0.5, 0.7, '中高相似度(0.5-0.7)'),
                (0.7, 0.9, '高相似度(0.7-0.9)'),
                (0.9, 1.0, '极高相似度(0.9-1.0)')
            ]
            
            distribution_stats = []
            for low, high, label in similarity_ranges:
                if high == 1.0:
                    count = len(all_results_df[all_results_df['综合相似度'] >= low])
                else:
                    count = len(all_results_df[(all_results_df['综合相似度'] >= low) & (all_results_df['综合相似度'] < high)])
                
                percentage = count / len(all_results_df) * 100 if len(all_results_df) > 0 else 0
                distribution_stats.append({
                    '相似度范围': label,
                    '数量': count,
                    '占比': f"{percentage:.1f}%"
                })
            
            distribution_df = pd.DataFrame(distribution_stats)
            distribution_df.to_excel(writer, sheet_name='相似度分布统计', index=False)
            self.log_debug(f"✅ 相似度分布统计: {len(distribution_df)} 个范围")
            
            # 5. 特征相似度分析
            feature_columns = ['TSN相似度', 'TSR相似度', 'CSN相似度', '维修次数相似度', '报价相似度', '维修序列相似度']
            feature_stats = []
            
            for feature in feature_columns:
                if feature in all_results_df.columns:
                    feature_data = all_results_df[feature].dropna()
                    if len(feature_data) > 0:
                        feature_stats.append({
                            '特征': feature,
                            '平均值': f"{feature_data.mean():.3f}",
                            '中位数': f"{feature_data.median():.3f}",
                            '标准差': f"{feature_data.std():.3f}",
                            '最小值': f"{feature_data.min():.3f}",
                            '最大值': f"{feature_data.max():.3f}"
                        })
            
            if feature_stats:
                feature_df = pd.DataFrame(feature_stats)
                feature_df.to_excel(writer, sheet_name='特征相似度统计', index=False)
                self.log_debug(f"✅ 特征相似度统计: {len(feature_df)} 个特征")
        
        self.log_debug(f"✅ 相似度分析结果已保存至: {output_file}")
        
        # 保存调试日志
        log_file = os.path.join(self.debug_dir, "similarity_analysis_log.txt")
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.debug_log))
        
        self.log_debug(f"✅ 调试日志已保存至: {log_file}")
        
        return output_file

    def save_simplified_similarity_table(self):
        """保存简化的相似度分析结果表格（四列格式）"""
        self.log_debug("=== 保存简化相似度分析结果表格 ===", "STEP")
        
        if not self.similarity_results:
            self.log_debug("❌ 无相似度分析结果", "ERROR")
            return None
        
        # 筛选高相似度结果（≥阈值）
        high_similarity_results = [
            result for result in self.similarity_results 
            if result['综合相似度'] >= self.similarity_threshold
        ]
        
        if not high_similarity_results:
            self.log_debug("⚠️ 无高相似度结果，将保存所有结果")
            high_similarity_results = self.similarity_results
        
        # 创建简化的四列表格
        simplified_data = []
        for result in high_similarity_results:
            simplified_data.append({
                '目标件号': result['目标件号'],
                '目标序号': result['目标序号'],
                '相似序号': result['备选序号'],
                '相似度': f"{result['综合相似度']:.3f}"
            })
        
        # 转换为DataFrame并排序
        simplified_df = pd.DataFrame(simplified_data)
        simplified_df = simplified_df.sort_values(['目标件号', '目标序号', '相似度'], 
                                                ascending=[True, True, False])
        
        # 保存到Excel文件
        simplified_output_file = os.path.join(self.output_dir, f"相似度分析结果表格_{MODEL_VERSION}.xlsx")
        
        with pd.ExcelWriter(simplified_output_file, engine='openpyxl') as writer:
            # 主要结果表格（四列格式）
            simplified_df.to_excel(writer, sheet_name='相似度分析结果', index=False)
            self.log_debug(f"✅ 简化相似度分析结果表格: {len(simplified_df)} 行")
            
            # 添加统计信息
            stats_data = [
                {'统计项': '总序号对数', '数值': len(simplified_df)},
                {'统计项': '涉及件号数', '数值': simplified_df['目标件号'].nunique()},
                {'统计项': '涉及目标序号数', '数值': simplified_df['目标序号'].nunique()},
                {'统计项': '涉及相似序号数', '数值': simplified_df['相似序号'].nunique()},
                {'统计项': '平均相似度', '数值': f"{simplified_df['相似度'].astype(float).mean():.3f}"},
                {'统计项': '最高相似度', '数值': simplified_df['相似度'].astype(float).max()},
                {'统计项': '最低相似度', '数值': simplified_df['相似度'].astype(float).min()}
            ]
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            self.log_debug(f"✅ 统计信息: {len(stats_df)} 项")
            
            # 按件号分组统计
            part_summary = simplified_df.groupby('目标件号').agg({
                '目标序号': 'nunique',
                '相似序号': 'nunique',
                '相似度': lambda x: x.astype(float).mean()
            }).reset_index()
            
            part_summary.columns = ['件号', '目标序号数', '相似序号数', '平均相似度']
            part_summary['平均相似度'] = part_summary['平均相似度'].apply(lambda x: f"{x:.3f}")
            part_summary = part_summary.sort_values('目标序号数', ascending=False)
            
            part_summary.to_excel(writer, sheet_name='按件号统计', index=False)
            self.log_debug(f"✅ 按件号统计: {len(part_summary)} 个件号")
        
        self.log_debug(f"✅ 简化相似度分析结果表格已保存至: {simplified_output_file}")
        
        # 同时保存为CSV格式（便于其他工具使用）
        csv_output_file = os.path.join(self.output_dir, f"相似度分析结果表格_{MODEL_VERSION}.csv")
        simplified_df.to_csv(csv_output_file, index=False, encoding='utf-8-sig')
        self.log_debug(f"✅ CSV格式结果已保存至: {csv_output_file}")
        
        return simplified_output_file

    def apply_input_cleaning_rules(self, df: pd.DataFrame) -> pd.DataFrame:
        """根据配置规则清洗原始输入数据"""
        try:
            rules = self.data_cleaning_rules or DEFAULT_CLEANING_RULES
            df_clean = df.copy()
            # 统一列名去空格
            df_clean.columns = df_clean.columns.map(str).str.strip()

            # 剔除组异常类型
            group_col = '组异常类型'
            if group_col in df_clean.columns:
                to_exclude = set([str(x).strip() for x in rules.get('exclude_group_abnormal_types', []) if str(x).strip()])
                if to_exclude:
                    mask = df_clean[group_col].astype(str).isin(to_exclude)
                    removed = int(mask.sum())
                    if removed > 0:
                        self.log_debug(f"按组异常类型剔除 {removed} 条: {sorted(list(to_exclude))}")
                    df_clean = df_clean[~mask]

            # 剔除单条记录异常类型
            single_col = '单条记录异常类型'
            if single_col in df_clean.columns:
                to_exclude = set([str(x).strip() for x in rules.get('exclude_single_record_abnormal_types', []) if str(x).strip()])
                if to_exclude:
                    mask = df_clean[single_col].astype(str).isin(to_exclude)
                    removed = int(mask.sum())
                    if removed > 0:
                        self.log_debug(f"按单条记录异常类型剔除 {removed} 条: {sorted(list(to_exclude))}")
                    df_clean = df_clean[~mask]

            df_clean.reset_index(drop=True, inplace=True)
            return df_clean
        except Exception as e:
            self.log_debug(f"数据清洗规则应用失败: {e}", "ERROR")
            return df

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 件序号相似度分析系统")
    print("=" * 80)
    print(f"📁 输出目录: {OUTPUT_BASE_PATH}")
    print(f"🎯 相似度阈值: {SIMILARITY_THRESHOLD}")
    print(f"📊 最大相似序号数: {TOP_SIMILAR_COUNT}")
    print(f"📋 最小维修记录数: {MIN_SERIAL_RECORDS}")
    print("=" * 80)
    
    # 可在此处调整超参数
    analyzer = SimilarityAnalyzer(
        similarity_threshold=0.7,  # 相似度阈值
        top_similar_count=20,      # 每个序号保留的最大相似序号数量
        data_cleaning_rules=DEFAULT_CLEANING_RULES
    )
    
    result = analyzer.run_similarity_analysis()
    
    if result:
        print(f"\n🎉 件序号相似度分析成功完成！")
        print(f"📋 简化表格文件: {result}")
        print(f"📊 输出目录: {analyzer.output_dir}")
        print("\n📋 生成的文件说明:")
        print("  1. 相似度分析结果表格_V1.0.xlsx - 主要结果（四列格式）")
        print("  2. 相似度分析结果表格_V1.0.csv - CSV格式结果")
        print("  3. 件序号相似度分析结果_V1.0.xlsx - 详细分析结果")
        print("  4. similarity_analysis_log.txt - 分析日志")
        print("\n📊 四列表格格式:")
        print("  列1: 目标件号")
        print("  列2: 目标序号") 
        print("  列3: 相似序号")
        print("  列4: 相似度")
    else:
        print("\n❌ 分析失败，请检查错误信息")

if __name__ == '__main__':
    main() 