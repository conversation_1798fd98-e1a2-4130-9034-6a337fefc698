# -*- coding: utf-8 -*-
"""
为指定 目标件号-目标序号 构建加权KM生存曲线（使用相似序号及其相似度作为权重）

功能：
1) 读取维修历史（与原 INPUT_FILE_PATH 相同格式）做必要的数据处理
2) 读取 飞机数据/按件号TSR预测与置信度分析结果_KM生存曲线.xlsx 的 “同件号高相似序号(>=0.7)” sheet
3) 根据输入的 目标件号-目标序号 与 相似序号 列表，从sheet查找相似度，构造 (相似序号, 相似度)
4) 复用 part_based_tsr_confidence_analyzer.py 的加权KM算法构建曲线，生成并保存：
   - 加权KM曲线数据表（含95%CI）
   - 加权KM曲线图（含95%CI阴影）
5) 输出目录：codes/件序号级TSR计算+置信度/test/KM_Curve_target/时间戳/

使用：
- 直接运行后按提示输入 目标件号、目标序号、相似序号（可用逗号分隔多个）。
- 如需固定参数，可将下方 DEFAULT_* 常量修改为你的路径。
"""
import os
import sys
import datetime
from pathlib import Path
from typing import Tuple
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse
import json

# 使脚本可直接运行（无需安装为包）
CURRENT_DIR = Path(__file__).resolve().parent
if str(CURRENT_DIR) not in sys.path:
    sys.path.insert(0, str(CURRENT_DIR))

# 添加项目根目录到路径，以便导入数据库配置
PROJECT_ROOT = CURRENT_DIR.parent
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

try:
    from sqlalchemy import create_engine, text
    from mu_air_fore.config.db_settings import SQLALCHEMY_URL

    DB_AVAILABLE = True
except ImportError:
    print("警告：无法导入数据库模块，将跳过数据库写入功能")
    DB_AVAILABLE = False

from part_based_tsr_confidence_analyzer2 import (
    PartBasedTSRConfidenceAnalyzer,
    INPUT_FILE_PATH as DEFAULT_REPAIR_HISTORY_FILE,
)

# 常量路径（可按需修改）
AIRCRAFT_DATA_DIR = CURRENT_DIR / "飞机数据"
SIMILARITY_EXCEL = AIRCRAFT_DATA_DIR / "按件号TSR预测与置信度分析结果_KM生存曲线.xlsx"
OUTPUT_BASE_DIR = CURRENT_DIR / "飞机数据" / "KM_Curve_target"

# 数据库表名
DB_CURVE_TABLE = "km_survival_curve"
DB_STAT_TABLE = "km_survival_curve_stat"


def _ensure_output_dir() -> Path:
    ts = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    out_dir = OUTPUT_BASE_DIR / ts
    out_dir.mkdir(parents=True, exist_ok=True)
    return out_dir


def _read_similarity_sheet(similarity_file: Path) -> pd.DataFrame:
    df = pd.read_excel(similarity_file, sheet_name='同件号高相似序号(>=0.7)')
    df.columns = df.columns.map(str).str.strip()
    # 期望列：目标件号, 目标序号, 相似序号, 相似度
    return df


def _get_similar_list(df_sim: pd.DataFrame, target_part: str, target_serial: str, sim_serials: list) -> list:
    # 过滤目标
    sub = df_sim[(df_sim['目标件号'] == target_part) & (df_sim['目标序号'].astype(str) == str(target_serial))]
    if len(sub) == 0:
        return []
    # 取相似序号与相似度
    sub['相似序号'] = sub['相似序号'].astype(str)
    wanted = [s.strip() for s in sim_serials if s and str(s).strip()]
    if wanted:
        sub = sub[sub['相似序号'].isin(wanted)]
    return [(row['相似序号'], float(row['相似度'])) for _, row in sub.iterrows()]


def _plot_km_with_ci(timeline, survival, ci_lower, ci_upper, title: str, save_path: Path):
    plt.figure(figsize=(8, 5))
    plt.plot(timeline, survival, label='加权KM生存曲线', color='#1f77b4', linewidth=2)
    if ci_lower is not None and ci_upper is not None:
        plt.fill_between(timeline, ci_lower, ci_upper, color='#1f77b4', alpha=0.15, label='95%置信区间')
    plt.xlabel('TSR')
    plt.ylabel('生存概率')
    plt.title(title)
    plt.grid(alpha=0.3)
    plt.legend()
    plt.tight_layout()
    plt.savefig(save_path, dpi=150)
    plt.close()


def _save_to_database(df_curve: pd.DataFrame, df_stats: pd.DataFrame,
                      target_part: str, target_serial: str, model_version: str) -> bool:
    """
    将KM曲线数据和统计数据保存到数据库

    Args:
        df_curve: KM曲线数据
        df_stats: 统计数据
        target_part: 件号
        target_serial: 序号
        model_version: 模型版本

    Returns:
        bool: 成功返回True，失败返回False
    """
    if not DB_AVAILABLE:
        print("跳过数据库写入：数据库模块不可用")
        return False

    try:
        eng = create_engine(SQLALCHEMY_URL, echo=False)

        # 准备曲线数据
        df_curve_db = df_curve.copy()
        df_curve_db['pnr'] = target_part
        df_curve_db['seq'] = target_serial
        df_curve_db['model_version'] = model_version

        # 如果需要转换为百分比形式（0-1 转为 0-100），取消下面的注释
        # df_curve_db['survival_probability'] = df_curve_db['survival_probability'] * 100
        # if 'ci_lower_0.95' in df_curve_db.columns:
        #     df_curve_db['ci_lower_0.95'] = df_curve_db['ci_lower_0.95'] * 100
        # if 'ci_upper_0.95' in df_curve_db.columns:
        #     df_curve_db['ci_upper_0.95'] = df_curve_db['ci_upper_0.95'] * 100

        # 重命名列以匹配数据库字段
        df_curve_db = df_curve_db.rename(columns={
            'timeline': 'timeline',
            'survival_probability': 'survival_probability',
            'ci_lower_0.95': 'ci_lower_0_95',
            'ci_upper_0.95': 'ci_upper_0_95'
        })

        # 选择需要的列
        curve_cols = ['pnr', 'seq', 'model_version', 'timeline', 'survival_probability', 'ci_lower_0_95',
                      'ci_upper_0_95']
        df_curve_final = df_curve_db[curve_cols]

        # 写入曲线数据
        df_curve_final.to_sql(
            name=DB_CURVE_TABLE,
            con=eng,
            if_exists="append",
            index=False,
            chunksize=1000,
            method="multi"
        )

        # 准备统计数据
        df_stats_db = df_stats.copy()
        df_stats_db['pnr'] = target_part
        df_stats_db['seq'] = target_serial
        df_stats_db['model_version'] = model_version

        # 重命名列以匹配数据库字段
        df_stats_db = df_stats_db.rename(columns={
            '指标': 'key',
            '值': 'value'
        })

        # 格式化value列：数值保留2位小数，非数值保持原样
        def format_value(val):
            try:
                # 尝试转换为浮点数
                num_val = float(val)
                # 如果是整数，显示为整数；否则保留2位小数
                if num_val == int(num_val):
                    return str(int(num_val))
                else:
                    return f"{num_val:.2f}"
            except (ValueError, TypeError):
                # 如果不是数值，保持原样
                return str(val)

        df_stats_db['value'] = df_stats_db['value'].apply(format_value)

        # 选择需要的列
        stat_cols = ['pnr', 'seq', 'model_version', 'key', 'value']
        df_stats_final = df_stats_db[stat_cols]

        # 写入统计数据
        df_stats_final.to_sql(
            name=DB_STAT_TABLE,
            con=eng,
            if_exists="append",
            index=False,
            chunksize=1000,
            method="multi"
        )

        # 反馈结果
        with eng.begin() as conn:
            curve_total = conn.execute(text(f"SELECT COUNT(*) FROM `{DB_CURVE_TABLE}`")).scalar()
            stat_total = conn.execute(text(f"SELECT COUNT(*) FROM `{DB_STAT_TABLE}`")).scalar()

        print(f"✅ 数据库写入成功:")
        print(f"   - {DB_CURVE_TABLE}: 新增 {len(df_curve_final)} 行，总计 {curve_total} 行")
        print(f"   - {DB_STAT_TABLE}: 新增 {len(df_stats_final)} 行，总计 {stat_total} 行")

        return True

    except Exception as e:
        print(f"❌ 数据库写入失败: {e}")
        return False


def _weighted_median(values: np.ndarray, weights: np.ndarray) -> float:
    """计算带权中位数。若权重全为0或数据为空，返回 np.nan。
    实现：按值排序后累积权重，找到累计权重跨越总权重一半的位置。
    """
    if values is None or len(values) == 0:
        return np.nan
    v = np.asarray(values, dtype=float)
    if weights is None:
        return np.median(v)
    w = np.asarray(weights, dtype=float)
    if len(v) != len(w) or np.all(w <= 0) or np.nansum(w) == 0:
        return np.nan
    # 排序
    sorter = np.argsort(v)
    v_sorted = v[sorter]
    w_sorted = w[sorter]
    cum_w = np.cumsum(w_sorted)
    cutoff = 0.5 * np.sum(w_sorted)
    idx = np.searchsorted(cum_w, cutoff, side='left')
    idx = min(max(idx, 0), len(v_sorted) - 1)
    return float(v_sorted[idx])


def _recalculate_confidence_intervals(timeline: np.ndarray, survival: np.ndarray, total_points: int) -> Tuple[
    np.ndarray, np.ndarray]:
    """
    重新计算置信区间，确保数据完整性
    使用Greenwood公式计算标准误差，然后计算95%置信区间
    """
    try:
        if len(timeline) != len(survival):
            print(f"⚠️ 时间轴和生存概率长度不一致: timeline={len(timeline)}, survival={len(survival)}")
            return None, None

        if total_points < 3:
            print(f"⚠️ 数据点不足({total_points})，无法计算置信区间")
            return None, None

        ci_lower = []
        ci_upper = []

        for i, prob in enumerate(survival):
            if pd.isna(prob):
                ci_lower.append(None)
                ci_upper.append(None)
            elif prob > 0 and prob < 1:
                # 使用Greenwood公式计算标准误差
                se = prob * np.sqrt((1 - prob) / total_points)
                # 95%置信区间
                lower = max(0.0, prob - 1.96 * se)
                upper = min(1.0, prob + 1.96 * se)
                ci_lower.append(lower)
                ci_upper.append(upper)
            else:
                # 对于概率为0或1的情况，置信区间就是概率本身
                ci_lower.append(prob)
                ci_upper.append(prob)

        print(f"✅ 重新计算置信区间完成，数据点: {len(ci_lower)}")
        return np.array(ci_lower), np.array(ci_upper)

    except Exception as e:
        print(f"❌ 重新计算置信区间失败: {e}")
        return None, None


def _validate_km_curve_data(serial: str, survival_curve: dict) -> bool:
    """验证KM生存曲线数据的完整性，特别检查TSR最大值时的置信区间"""
    try:
        kmf = survival_curve['kmf']
        timeline = kmf.survival_function_.index.values
        survival_prob = kmf.survival_function_['KM_estimate'].values

        if len(timeline) == 0 or len(survival_prob) == 0:
            print(f"❌ 序号 {serial}: 时间轴或生存概率为空")
            return False

        # 检查TSR最大值时的数据
        max_tsr_idx = np.argmax(timeline)
        max_tsr = timeline[max_tsr_idx]
        max_survival_prob = survival_prob[max_tsr_idx]

        print(f"序号 {serial}: TSR最大值={max_tsr:.1f}, 对应生存概率={max_survival_prob:.6f}")

        # 检查置信区间
        ci_lower = None
        ci_upper = None
        if hasattr(kmf, 'confidence_interval_') and kmf.confidence_interval_ is not None:
            ci_df = kmf.confidence_interval_
            if 'KM_estimate_lower_0.95' in ci_df.columns and 'KM_estimate_upper_0.95' in ci_df.columns:
                ci_lower = ci_df['KM_estimate_lower_0.95'].values
                ci_upper = ci_df['KM_estimate_upper_0.95'].values

                if len(ci_lower) > max_tsr_idx and len(ci_upper) > max_tsr_idx:
                    max_ci_lower = ci_lower[max_tsr_idx]
                    max_ci_upper = ci_upper[max_tsr_idx]

                    # 检查置信区间是否合理
                    if max_ci_lower <= max_survival_prob <= max_ci_upper:
                        print(f"✅ 序号 {serial}: TSR最大值时置信区间正常 [{max_ci_lower:.6f}, {max_ci_upper:.6f}]")
                    else:
                        print(
                            f"⚠️ 序号 {serial}: TSR最大值时置信区间异常! 生存概率={max_survival_prob:.6f}, CI=[{max_ci_lower:.6f}, {max_ci_upper:.6f}]")

                        # 检查偏差程度
                        if max_ci_lower > max_survival_prob:
                            lower_deviation = (
                                                          max_ci_lower - max_survival_prob) / max_survival_prob if max_survival_prob > 0 else float(
                                'inf')
                            print(f"⚠️ 置信区间下界偏高: 偏差={lower_deviation:.1%}")

                        if max_ci_upper < max_survival_prob:
                            upper_deviation = (
                                                          max_survival_prob - max_ci_upper) / max_survival_prob if max_survival_prob > 0 else float(
                                'inf')
                            print(f"⚠️ 置信区间上界偏低: 偏差={upper_deviation:.1%}")
                else:
                    print(f"⚠️ 序号 {serial}: 置信区间长度不足，无法检查TSR最大值")
            else:
                print(f"⚠️ 序号 {serial}: 置信区间列缺失")
        else:
            print(f"⚠️ 序号 {serial}: 置信区间对象缺失")

        # 检查数据一致性
        timeline_length = len(timeline)
        survival_length = len(survival_prob)
        ci_lower_length = len(ci_lower) if ci_lower is not None else 0
        ci_upper_length = len(ci_upper) if ci_upper is not None else 0

        if timeline_length != survival_length:
            print(f"❌ 序号 {serial}: 时间轴({timeline_length})与生存概率({survival_length})长度不一致")
            return False

        if ci_lower is not None and ci_upper is not None:
            if ci_lower_length != timeline_length or ci_upper_length != timeline_length:
                print(
                    f"⚠️ 序号 {serial}: 置信区间长度不一致 - timeline:{timeline_length}, ci_lower:{ci_lower_length}, ci_upper:{ci_upper_length}")

        return True

    except Exception as e:
        print(f"❌ 序号 {serial} KM曲线数据验证失败: {e}")
        return False


def _check_max_tsr_confidence_interval(timeline: np.ndarray, survival: np.ndarray, ci_lower: np.ndarray,
                                       ci_upper: np.ndarray) -> None:
    """检查TSR最大值时的置信区间合理性"""
    try:
        if len(timeline) == 0:
            return

        max_tsr_idx = np.argmax(timeline)
        max_tsr = timeline[max_tsr_idx]
        max_survival_prob = survival[max_tsr_idx]

        print(f"🔍 TSR最大值时置信区间检查:")
        print(f"  - TSR最大值: {max_tsr:.1f}")
        print(f"  - 对应生存概率: {max_survival_prob:.6f}")

        if ci_lower is not None and ci_upper is not None and len(ci_lower) > max_tsr_idx and len(
                ci_upper) > max_tsr_idx:
            max_ci_lower = ci_lower[max_tsr_idx]
            max_ci_upper = ci_upper[max_tsr_idx]

            print(f"  - 置信区间下界: {max_ci_lower:.6f}")
            print(f"  - 置信区间上界: {max_ci_upper:.6f}")

            # 检查置信区间是否合理
            if max_ci_lower <= max_survival_prob <= max_ci_upper:
                print(f"  ✅ 置信区间正常: [{max_ci_lower:.6f}, {max_ci_upper:.6f}]")
            else:
                print(
                    f"  ⚠️ 置信区间异常! 生存概率={max_survival_prob:.6f}, CI=[{max_ci_lower:.6f}, {max_ci_upper:.6f}]")

                # 检查偏差程度
                if max_ci_lower > max_survival_prob:
                    lower_deviation = (
                                                  max_ci_lower - max_survival_prob) / max_survival_prob if max_survival_prob > 0 else float(
                        'inf')
                    print(f"  ⚠️ 置信区间下界偏高: 偏差={lower_deviation:.1%}")

                if max_ci_upper < max_survival_prob:
                    upper_deviation = (
                                                  max_survival_prob - max_ci_upper) / max_survival_prob if max_survival_prob > 0 else float(
                        'inf')
                    print(f"  ⚠️ 置信区间上界偏低: 偏差={upper_deviation:.1%}")
        else:
            print(f"  ⚠️ 无法检查置信区间（数据不足）")

    except Exception as e:
        print(f"❌ TSR最大值置信区间检查失败: {e}")


def build_weighted_km_for_target(
        repair_history_file: str = None,
        target_part_num: str = None,
        target_serial: str = None,
        similar_serials: list = None,
        model_version: str = "v1.0",
        save_to_db: bool = True,
) -> Path:
    # 路径准备
    repair_history_path = repair_history_file or str(DEFAULT_REPAIR_HISTORY_FILE)
    similarity_file = SIMILARITY_EXCEL
    out_dir = _ensure_output_dir()

    print(f"📂 维修历史: {repair_history_path}")
    print(f"📄 相似序号文件: {similarity_file}")
    print(f"📁 输出目录: {out_dir}")

    # 读取维修历史并处理
    raw_df = pd.read_excel(repair_history_path)
    analyzer = PartBasedTSRConfidenceAnalyzer(enable_step1_prediction=False)
    print("🔄 处理维修历史数据...")
    processed_df = analyzer.tsr_analyzer.process_data(raw_df)

    # 读取相似度sheet
    print("🔄 读取相似序号sheet...")
    df_sim = _read_similarity_sheet(similarity_file)

    # 目标与相似序号解析
    if similar_serials is None:
        similar_serials = []
    sim_list = _get_similar_list(df_sim, str(target_part_num), str(target_serial), similar_serials)
    if not sim_list:
        raise ValueError("未找到匹配的相似序号与相似度，请检查目标/相似序号或sheet内容。")
    print(f"✅ 相似序号及相似度: {sim_list}")

    # 准备目标数据与件号数据
    part_df = processed_df[processed_df['件号'] == target_part_num].copy()
    target_data = part_df[part_df['序号'].astype(str) == str(target_serial)].sort_values('第几次维修').copy()
    if len(target_data) == 0:
        raise ValueError("目标序号在维修历史数据中不存在或无记录。")

    # 构建加权KM曲线（使用原脚本算法）
    print("🚀 构建加权KM生存曲线...")
    survival_curve = analyzer.build_weighted_km_curve_no_plot(
        target_serial=str(target_serial),
        target_data=target_data,
        similar_serials=sim_list,
        part_df=part_df,
    )
    if survival_curve is None:
        raise RuntimeError("加权KM曲线构建失败。")

    # 新增：验证KM生存曲线数据完整性
    if _validate_km_curve_data(str(target_serial), survival_curve):
        print(f"✅ 序号 {target_serial} KM曲线数据验证通过")
    else:
        print(f"⚠️ 序号 {target_serial} KM曲线数据验证失败，可能存在置信区间问题")

    kmf = survival_curve['kmf']
    timeline = kmf.survival_function_.index.values
    survival = kmf.survival_function_['KM_estimate'].values

    # 获取生存数据用于统计
    surv_df = survival_curve.get('survival_data')
    total_points = len(surv_df) if surv_df is not None else 0

    # 改进：更健壮的置信区间处理
    ci_lower = None
    ci_upper = None
    if hasattr(kmf, 'confidence_interval_') and kmf.confidence_interval_ is not None:
        ci_df = kmf.confidence_interval_
        if 'KM_estimate_lower_0.95' in ci_df.columns and 'KM_estimate_upper_0.95' in ci_df.columns:
            ci_lower = ci_df['KM_estimate_lower_0.95'].values
            ci_upper = ci_df['KM_estimate_upper_0.95'].values

            # 检查置信区间长度一致性
            if len(ci_lower) != len(timeline) or len(ci_upper) != len(timeline):
                print(
                    f"⚠️ 置信区间长度不一致: timeline={len(timeline)}, ci_lower={len(ci_lower)}, ci_upper={len(ci_upper)}")
                # 重新计算置信区间
                ci_lower, ci_upper = _recalculate_confidence_intervals(timeline, survival, total_points)

    # 如果置信区间缺失，重新计算
    if ci_lower is None or ci_upper is None:
        print("置信区间缺失，重新计算")
        ci_lower, ci_upper = _recalculate_confidence_intervals(timeline, survival, total_points)

    # 确保所有数组长度一致
    min_length = min(len(timeline), len(survival), len(ci_lower), len(ci_upper))
    timeline = timeline[:min_length]
    survival = survival[:min_length]
    ci_lower = ci_lower[:min_length] if ci_lower is not None else [None] * min_length
    ci_upper = ci_upper[:min_length] if ci_upper is not None else [None] * min_length

    # 统计：数据量、TSR均值/中位数（含加权）
    stats_rows = []
    if surv_df is not None and len(surv_df) > 0:
        target_df = surv_df[surv_df['序号类型'] == '目标序号']
        similar_df = surv_df[surv_df['序号类型'] == '相似序号']
        # 基本计数
        target_points = int(len(target_df))
        similar_points = int(len(similar_df))
        unique_similar_serials = int(similar_df['序号'].nunique()) if similar_points > 0 else 0
        total_points = int(len(surv_df))
        # 均值/中位数（未加权）
        target_mean = float(target_df['生存时间_TSR'].mean()) if target_points > 0 else np.nan
        target_median = float(target_df['生存时间_TSR'].median()) if target_points > 0 else np.nan
        similar_mean = float(similar_df['生存时间_TSR'].mean()) if similar_points > 0 else np.nan
        similar_median = float(similar_df['生存时间_TSR'].median()) if similar_points > 0 else np.nan
        combined_mean = float(surv_df['生存时间_TSR'].mean())
        combined_median = float(surv_df['生存时间_TSR'].median())
        # 加权统计（按相似度权重；目标权重=1.0）
        try:
            weighted_mean = float(np.average(surv_df['生存时间_TSR'].values, weights=surv_df['相似度权重'].values))
        except Exception:
            weighted_mean = np.nan
        weighted_median = _weighted_median(surv_df['生存时间_TSR'].values, surv_df['相似度权重'].values)

        stats_rows = [
            {'指标': '总数据点', '值': total_points},
            {'指标': '目标序号数据点', '值': target_points},
            {'指标': '相似序号数据点(合计)', '值': similar_points},
            {'指标': '相似序号数量(去重)', '值': unique_similar_serials},
            {'指标': '目标TSR_平均值', '值': target_mean},
            {'指标': '目标TSR_中位数', '值': target_median},
            {'指标': '相似TSR_平均值', '值': similar_mean},
            {'指标': '相似TSR_中位数', '值': similar_median},
            {'指标': '合并TSR_平均值', '值': combined_mean},
            {'指标': '合并TSR_中位数', '值': combined_median},
            {'指标': '加权TSR_平均值', '值': weighted_mean},
            {'指标': '加权TSR_中位数', '值': weighted_median},
        ]

        # 打印简要统计
        print("📊 统计汇总：")
        print(
            f"  - 目标数据点: {target_points} | 相似数据点(合计): {similar_points} | 相似序号数: {unique_similar_serials}")
        print(f"  - 目标TSR(均值/中位数): {target_mean:.2f} / {target_median:.2f}")
        if similar_points > 0:
            print(f"  - 相似TSR(均值/中位数): {similar_mean:.2f} / {similar_median:.2f}")
        print(f"  - 合并TSR(均值/中位数): {combined_mean:.2f} / {combined_median:.2f}")
        if not np.isnan(weighted_mean):
            wm_str = 'NaN' if np.isnan(weighted_median) else f"{weighted_median:.2f}"
            print(f"  - 加权TSR(均值/中位数): {weighted_mean:.2f} / {wm_str}")

    # 保存数据表
    df_out = pd.DataFrame({
        'timeline': timeline,
        'survival_probability': survival,
        'ci_lower_0.95': ci_lower if ci_lower is not None else [None] * len(timeline),
        'ci_upper_0.95': ci_upper if ci_upper is not None else [None] * len(timeline),
    })

    # 检查数据完整性
    print(f"📊 数据完整性检查:")
    print(f"  - 时间轴长度: {len(timeline)}")
    print(f"  - 生存概率长度: {len(survival)}")
    print(f"  - 置信区间下界长度: {len(ci_lower) if ci_lower is not None else 0}")
    print(f"  - 置信区间上界长度: {len(ci_upper) if ci_upper is not None else 0}")

    # 检查置信区间是否合理
    if ci_lower is not None and ci_upper is not None:
        valid_ci_count = sum(1 for i in range(len(timeline))
                             if ci_lower[i] is not None and ci_upper[i] is not None
                             and ci_lower[i] <= survival[i] <= ci_upper[i])
        print(f"  - 合理置信区间数量: {valid_ci_count}/{len(timeline)}")

        if valid_ci_count < len(timeline):
            print(f"⚠️ 存在 {len(timeline) - valid_ci_count} 个不合理的置信区间")

    # 新增：检查TSR最大值时的置信区间合理性
    _check_max_tsr_confidence_interval(timeline, survival, ci_lower, ci_upper)

    excel_path = out_dir / f"加权KM_{target_part_num}_{target_serial}.xlsx"
    df_out.to_excel(excel_path, index=False)
    print(f"✅ 已保存KM表: {excel_path}")

    # 保存图表
    title = f"加权KM曲线 - 件号 {target_part_num} / 序号 {target_serial}"
    img_path = out_dir / f"加权KM_{target_part_num}_{target_serial}.png"
    _plot_km_with_ci(timeline, survival, ci_lower, ci_upper, title, img_path)
    print(f"✅ 已保存KM图: {img_path}")

    # 保存相似序号明细
    sim_df = pd.DataFrame(sim_list, columns=['相似序号', '相似度'])
    sim_detail_path = out_dir / f"相似序号明细_{target_part_num}_{target_serial}.xlsx"
    sim_df.to_excel(sim_detail_path, index=False)
    print(f"✅ 已保存相似序号明细: {sim_detail_path}")

    # 保存统计汇总
    stats_df = None
    if stats_rows:
        stats_df = pd.DataFrame(stats_rows)
        stats_path = out_dir / f"统计汇总_{target_part_num}_{target_serial}.xlsx"
        stats_df.to_excel(stats_path, index=False)
        print(f"✅ 已保存统计汇总: {stats_path}")

    # 写入数据库
    if save_to_db and stats_df is not None:
        print("🔄 写入数据库...")
        _save_to_database(df_out, stats_df, target_part_num, target_serial, model_version)

    return out_dir


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='构建加权KM生存曲线',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python build_weighted_km_for_target_V1.py '{"1706903": {"AAB3001205": ["AAB3001251", "AAB3001762"]}}' v1.0

参数格式说明:
  第一个参数: JSON格式的任务配置，格式为 {"件号": {"目标序号": ["相似序号1", "相似序号2", ...]}}
  第二个参数: 模型版本号，默认为 "v1.0"
        '''
    )

    parser.add_argument(
        'tasks_json',
        help='JSON格式的任务配置，例如: \'{"1706903": {"AAB3001205": ["AAB3001251", "AAB3001762"]}}\''
    )

    parser.add_argument(
        'model_version',
        nargs='?',
        default='v1.0',
        help='模型版本号，默认为 "v1.0"'
    )

    parser.add_argument(
        '--no-db',
        action='store_true',
        help='不写入数据库，仅生成Excel文件'
    )

    return parser.parse_args()


def parse_tasks_from_json(tasks_json: str) -> list:
    """
    从JSON字符串解析任务列表

    Args:
        tasks_json: JSON格式的任务配置

    Returns:
        list: 任务列表，格式为 [(件号, 目标序号, [相似序号列表]), ...]
    """
    try:
        tasks_dict = json.loads(tasks_json)
        tasks = []

        for part_num, serials_dict in tasks_dict.items():
            for target_serial, similar_serials in serials_dict.items():
                tasks.append((part_num, target_serial, similar_serials))

        return tasks

    except json.JSONDecodeError as e:
        raise ValueError(f"JSON格式错误: {e}")
    except Exception as e:
        raise ValueError(f"任务配置解析错误: {e}")


if __name__ == "__main__":
    try:
        # 解析命令行参数
        args = parse_args()

        # 解析任务配置
        tasks = parse_tasks_from_json(args.tasks_json)

        if not tasks:
            print("⚠️ 未找到有效的任务配置")
            sys.exit(1)

        print(f"📋 解析到 {len(tasks)} 个任务")
        print(f"🏷️ 模型版本: {args.model_version}")
        print(f"💾 数据库写入: {'否' if args.no_db else '是'}")

        # 执行所有任务
        for i, (target_part, target_serial, sim_list) in enumerate(tasks, 1):
            print(f"\n=== 任务 {i}/{len(tasks)}: 件号={target_part}, 序号={target_serial}, 相似序号={sim_list} ===")
            build_weighted_km_for_target(
                repair_history_file=str(DEFAULT_REPAIR_HISTORY_FILE),
                target_part_num=str(target_part),
                target_serial=str(target_serial),
                similar_serials=[str(s) for s in sim_list],
                model_version=args.model_version,
                save_to_db=not args.no_db,
            )

        print(f"\n✅ 全部 {len(tasks)} 个任务处理完成")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)