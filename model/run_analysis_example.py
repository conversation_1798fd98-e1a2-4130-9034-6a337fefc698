#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TSR分析脚本使用示例
演示如何使用修改后的comprehensive_tsr_analyzer2.py进行分析
"""

from comprehensive_tsr_analyzer2 import main

def run_single_part_analysis():
    """运行单个件号分析示例"""
    print("=== 运行单个件号分析 ===")
    analyze_parts = ["1706903"]
    model_version = "v1.0"
    
    main(analyze_parts=analyze_parts, model_version=model_version)

def run_multiple_parts_analysis():
    """运行多个件号分析示例"""
    print("=== 运行多个件号分析 ===")
    analyze_parts = ["1706903", "740119G"]
    model_version = "v2.1"
    
    main(analyze_parts=analyze_parts, model_version=model_version)

def run_all_parts_analysis():
    """运行所有件号分析示例"""
    print("=== 运行所有件号分析 ===")
    analyze_parts = []  # 空列表表示分析所有件号
    model_version = "v1.5"
    
    main(analyze_parts=analyze_parts, model_version=model_version)

def run_default_analysis():
    """运行默认参数分析"""
    print("=== 运行默认参数分析 ===")
    # 使用默认参数：analyze_parts=["1706903"], model_version="v1.0"
    main()

if __name__ == "__main__":
    # 选择要运行的分析类型
    print("请选择要运行的分析类型：")
    print("1. 单个件号分析 (件号: 1706903, 版本: v1.0)")
    print("2. 多个件号分析 (件号: 1706903,740119G, 版本: v2.1)")
    print("3. 所有件号分析 (版本: v1.5)")
    print("4. 默认参数分析")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        run_single_part_analysis()
    elif choice == "2":
        run_multiple_parts_analysis()
    elif choice == "3":
        run_all_parts_analysis()
    elif choice == "4":
        run_default_analysis()
    else:
        print("无效选择，运行默认分析")
        run_default_analysis()
