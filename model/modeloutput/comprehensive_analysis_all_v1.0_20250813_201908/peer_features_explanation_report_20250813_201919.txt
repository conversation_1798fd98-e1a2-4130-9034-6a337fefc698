================================================================================
                    同伴特征计算详细解释报告
                 Peer Features Calculation Explanation Report
================================================================================

报告生成时间: 20250813_201919
数据处理概览: {'total_records': 1681, 'unique_parts': 6, 'parts_processed': 6, 'examples_collected': 0}

1. 同伴特征含义详解
==================================================
 1. peer_avg_tsr_tsn_repair       : TSN和维修次数都相似的同伴器材的平均TSR（排除异常值后）
 2. peer_median_tsr_tsn_repair    : TSN和维修次数都相似的同伴器材的TSR中位数（排除异常值后）
 3. peer_std_tsr_tsn_repair       : TSN和维修次数都相似的同伴器材的TSR标准差（排除异常值后）
 4. peer_count_tsn_repair         : TSN和维修次数都相似的同伴器材数量
 5. peer_outliers_removed_tsn_repair: TSN和维修次数相似同伴中被移除的异常值数量
 6. peer_avg_tsr_tsn_only         : 仅TSN相似的同伴器材的平均TSR（排除异常值后）
 7. peer_median_tsr_tsn_only      : 仅TSN相似的同伴器材的TSR中位数（排除异常值后）
 8. peer_std_tsr_tsn_only         : 仅TSN相似的同伴器材的TSR标准差（排除异常值后）
 9. peer_count_tsn_only           : 仅TSN相似的同伴器材数量
10. peer_outliers_removed_tsn_only: 仅TSN相似同伴中被移除的异常值数量
11. peer_avg_tsr_repair_only      : 仅维修次数相似的同伴器材的平均TSR（排除异常值后）
12. peer_median_tsr_repair_only   : 仅维修次数相似的同伴器材的TSR中位数（排除异常值后）
13. peer_std_tsr_repair_only      : 仅维修次数相似的同伴器材的TSR标准差（排除异常值后）
14. peer_count_repair_only        : 仅维修次数相似的同伴器材数量
15. peer_outliers_removed_repair_only: 仅维修次数相似同伴中被移除的异常值数量
16. part_total_records            : 该件号的总记录数
17. part_unique_serials           : 该件号的唯一序号数
18. part_avg_tsr                  : 该件号的平均TSR
19. part_std_tsr                  : 该件号的TSR标准差
20. peer_similarity_score         : 相似度得分（找到的同伴数/总可能同伴数）
21. is_outlier_in_part            : 是否为该件号中的异常值（Z-score>2）

2. 计算过程详细步骤
==================================================
步骤 1: 数据预处理
------------------------------
输入: 原始数据框，包含1681条记录
处理: 初始化18个同伴特征列为NaN值
输出: 增加了18个空的同伴特征列

步骤 2.1: 件号740119G处理
------------------------------
输入: 件号740119G的86条记录
处理: 计算件号级统计特征和异常值检测
详细信息:
  part_statistics:
    total_records: 86
    unique_serials: 32
    avg_tsr: 3733.8
    std_tsr: 4405.7
    tsr_range: 0.0 - 20192.4
  outlier_detection:
    outliers_count: 3
    outlier_rate: 3.5%
  peer_calculations:
    tsn_repair_pairs: 427
    tsn_only_pairs: 1217
    repair_only_pairs: 2368
    total_comparisons: 258
输出: 为86条记录计算了同伴特征

步骤 2.2: 件号761574B处理
------------------------------
输入: 件号761574B的792条记录
处理: 计算件号级统计特征和异常值检测
详细信息:
  part_statistics:
    total_records: 792
    unique_serials: 416
    avg_tsr: 6025.9
    std_tsr: 5506.5
    tsr_range: 0.0 - 24458.8
  outlier_detection:
    outliers_count: 44
    outlier_rate: 5.6%
  peer_calculations:
    tsn_repair_pairs: 52012
    tsn_only_pairs: 109529
    repair_only_pairs: 266432
    total_comparisons: 2367
输出: 为792条记录计算了同伴特征

步骤 2.3: 件号740119H处理
------------------------------
输入: 件号740119H的271条记录
处理: 计算件号级统计特征和异常值检测
详细信息:
  part_statistics:
    total_records: 271
    unique_serials: 116
    avg_tsr: 5582.8
    std_tsr: 5808.4
    tsr_range: 0.0 - 24701.6
  outlier_detection:
    outliers_count: 15
    outlier_rate: 5.5%
  peer_calculations:
    tsn_repair_pairs: 5814
    tsn_only_pairs: 13786
    repair_only_pairs: 29700
    total_comparisons: 810
输出: 为271条记录计算了同伴特征

步骤 2.4: 件号752168B处理
------------------------------
输入: 件号752168B的177条记录
处理: 计算件号级统计特征和异常值检测
详细信息:
  part_statistics:
    total_records: 177
    unique_serials: 82
    avg_tsr: 6653.8
    std_tsr: 6144.0
    tsr_range: 0.0 - 22509.8
  outlier_detection:
    outliers_count: 10
    outlier_rate: 5.6%
  peer_calculations:
    tsn_repair_pairs: 2303
    tsn_only_pairs: 5283
    repair_only_pairs: 12198
    total_comparisons: 531
输出: 为177条记录计算了同伴特征

步骤 2.5: 件号752168C处理
------------------------------
输入: 件号752168C的8条记录
处理: 计算件号级统计特征和异常值检测
详细信息:
  part_statistics:
    total_records: 8
    unique_serials: 8
    avg_tsr: 4759.6
    std_tsr: 3898.1
    tsr_range: 0.0 - 13236.5
  outlier_detection:
    outliers_count: 1
    outlier_rate: 12.5%
  peer_calculations:
    tsn_repair_pairs: 11
    tsn_only_pairs: 11
    repair_only_pairs: 56
    total_comparisons: 24
输出: 为8条记录计算了同伴特征

步骤 2.6: 件号1706903处理
------------------------------
输入: 件号1706903的347条记录
处理: 计算件号级统计特征和异常值检测
详细信息:
  part_statistics:
    total_records: 347
    unique_serials: 179
    avg_tsr: 6076.7
    std_tsr: 6216.3
    tsr_range: 0.0 - 24846.6
  outlier_detection:
    outliers_count: 28
    outlier_rate: 8.1%
  peer_calculations:
    tsn_repair_pairs: 7843
    tsn_only_pairs: 18730
    repair_only_pairs: 49188
    total_comparisons: 1041
输出: 为347条记录计算了同伴特征

步骤 3: 缺失值处理
------------------------------
输入: 计算完成的同伴特征数据
处理: 使用0填充缺失的同伴特征值
详细信息:
  missing_before_fill:
    peer_avg_tsr_tsn_repair: 96/1681 (5.7%)
    peer_median_tsr_tsn_repair: 96/1681 (5.7%)
    peer_avg_tsr_tsn_only: 47/1681 (2.8%)
    peer_median_tsr_tsn_only: 47/1681 (2.8%)
    peer_avg_tsr_repair_only: 8/1681 (0.5%)
    peer_median_tsr_repair_only: 8/1681 (0.5%)
输出: 所有同伴特征缺失值已填充为0

3. 同伴匹配计算示例
==================================================
示例 1: 件号 740119G
------------------------------
目标器材: 序号=3029, CSN=23267.0, 维修次数=5.0, TSR=0.0

  相似性策略: 仅维修次数匹配
    维修次数范围: 4.0 - 6.0 (容差±1)
    找到同伴数量: 81
    同伴器材详情:
      序号1934: CSN=30275.0, 维修次数=1.0, TSR=1094.9
      序号1663: CSN=34231.0, 维修次数=1.0, TSR=4662.1
      序号1663: CSN=36840.0, 维修次数=2.0, TSR=1825.6
      序号1768: CSN=11823.0, 维修次数=1.0, TSR=9434.4
      序号1768: CSN=17603.0, 维修次数=2.0, TSR=11445.1
    同伴TSR统计: 平均=3080.7, 中位数=1780.5, 标准差=3233.8, 范围=0.0-11445.1

  相似性策略: 仅维修次数匹配
    维修次数范围: 4.0 - 6.0 (容差±1)
    找到同伴数量: 81
    同伴器材详情:
      序号1934: CSN=30275.0, 维修次数=1.0, TSR=1094.9
      序号1663: CSN=34231.0, 维修次数=1.0, TSR=4662.1
      序号1663: CSN=36840.0, 维修次数=2.0, TSR=1825.6
      序号1768: CSN=11823.0, 维修次数=1.0, TSR=9434.4
      序号1768: CSN=17603.0, 维修次数=2.0, TSR=11445.1
    同伴TSR统计: 平均=3080.7, 中位数=1780.5, 标准差=3233.8, 范围=0.0-11445.1

  相似性策略: 仅维修次数匹配
    维修次数范围: 4.0 - 6.0 (容差±1)
    找到同伴数量: 16
    同伴器材详情:
      序号1934: CSN=29700.0, 维修次数=4.0, TSR=1079.3
      序号1934: CSN=30883.0, 维修次数=5.0, TSR=3414.2
      序号1934: CSN=32972.0, 维修次数=6.0, TSR=817.3
      序号2027: CSN=23157.0, 维修次数=4.0, TSR=1.8
      序号3020: CSN=42199.0, 维修次数=4.0, TSR=1093.2
    同伴TSR统计: 平均=1403.9, 中位数=1079.3, 标准差=1551.0, 范围=0.0-4770.6


示例 2: 件号 761574B
------------------------------
目标器材: 序号=AAAH006404, CSN=20913.0, 维修次数=2.0, TSR=4593.7

  相似性策略: 仅维修次数匹配
    维修次数范围: 1.0 - 3.0 (容差±1)
    找到同伴数量: 789
    同伴器材详情:
      序号0022: CSN=17330.0, 维修次数=1.0, TSR=4310.8
      序号0022: CSN=19549.0, 维修次数=2.0, TSR=3573.8
      序号0022: CSN=21681.0, 维修次数=3.0, TSR=1989.6
      序号0022: CSN=22961.0, 维修次数=4.0, TSR=22.1
      序号0022: CSN=22973.0, 维修次数=5.0, TSR=2.3
    同伴TSR统计: 平均=5747.3, 中位数=4459.8, 标准差=5097.0, 范围=0.0-19870.5

  相似性策略: 仅维修次数匹配
    维修次数范围: 1.0 - 3.0 (容差±1)
    找到同伴数量: 789
    同伴器材详情:
      序号0022: CSN=17330.0, 维修次数=1.0, TSR=4310.8
      序号0022: CSN=19549.0, 维修次数=2.0, TSR=3573.8
      序号0022: CSN=21681.0, 维修次数=3.0, TSR=1989.6
      序号0022: CSN=22961.0, 维修次数=4.0, TSR=22.1
      序号0022: CSN=22973.0, 维修次数=5.0, TSR=2.3
    同伴TSR统计: 平均=5747.3, 中位数=4459.8, 标准差=5097.0, 范围=0.0-19870.5

  相似性策略: 仅维修次数匹配
    维修次数范围: 1.0 - 3.0 (容差±1)
    找到同伴数量: 298
    同伴器材详情:
      序号0022: CSN=19549.0, 维修次数=2.0, TSR=3573.8
      序号0022: CSN=21681.0, 维修次数=3.0, TSR=1989.6
      序号0120: CSN=31993.0, 维修次数=2.0, TSR=4627.9
      序号0135: CSN=16534.0, 维修次数=2.0, TSR=600.6
      序号0135: CSN=16816.0, 维修次数=3.0, TSR=9802.5
    同伴TSR统计: 平均=5284.4, 中位数=4410.7, 标准差=4537.8, 范围=0.0-17721.2


示例 3: 件号 740119H
------------------------------
目标器材: 序号=AAAE004167, CSN=28511.0, 维修次数=2.0, TSR=79.3

  相似性策略: 仅维修次数匹配
    维修次数范围: 1.0 - 3.0 (容差±1)
    找到同伴数量: 268
    同伴器材详情:
      序号2010: CSN=12289.0, 维修次数=1.0, TSR=17462.2
      序号2064: CSN=15378.0, 维修次数=3.0, TSR=0.0
      序号3135: CSN=25875.0, 维修次数=1.0, TSR=15560.6
      序号3135: CSN=34795.0, 维修次数=2.0, TSR=3860.1
      序号3145: CSN=4760.0, 维修次数=1.0, TSR=9215.2
    同伴TSR统计: 平均=5233.2, 中位数=3595.9, 标准差=5247.3, 范围=0.0-21244.4

  相似性策略: 仅维修次数匹配
    维修次数范围: 1.0 - 3.0 (容差±1)
    找到同伴数量: 268
    同伴器材详情:
      序号2010: CSN=12289.0, 维修次数=1.0, TSR=17462.2
      序号2064: CSN=15378.0, 维修次数=3.0, TSR=0.0
      序号3135: CSN=25875.0, 维修次数=1.0, TSR=15560.6
      序号3135: CSN=34795.0, 维修次数=2.0, TSR=3860.1
      序号3145: CSN=4760.0, 维修次数=1.0, TSR=9215.2
    同伴TSR统计: 平均=5233.2, 中位数=3595.9, 标准差=5247.3, 范围=0.0-21244.4

  相似性策略: 仅维修次数匹配
    维修次数范围: 1.0 - 3.0 (容差±1)
    找到同伴数量: 125
    同伴器材详情:
      序号2064: CSN=15378.0, 维修次数=3.0, TSR=0.0
      序号3135: CSN=34795.0, 维修次数=2.0, TSR=3860.1
      序号3145: CSN=9884.0, 维修次数=2.0, TSR=10250.8
      序号3154: CSN=22869.0, 维修次数=2.0, TSR=6016.1
      序号3154: CSN=26497.0, 维修次数=3.0, TSR=51.1
    同伴TSR统计: 平均=4591.2, 中位数=2975.1, 标准差=4619.3, 范围=0.0-17543.2


4. 相似性匹配参数配置
==================================================
TSN容差比例: 15.0%
维修次数容差: ±1
最小同伴数量要求: 2
异常值检测阈值: Z-score > 2

5. 同伴匹配算法逻辑
==================================================
相似性匹配策略:
1. CSN+维修次数复合匹配:
   CSN条件: |CSN_peer - CSN_current| ≤ CSN_current × 0.15
   维修次数条件: 【改进】智能分层匹配策略
   用途: 找到使用强度和维修阶段都相似的器材

2. 仅CSN匹配:
   条件: |CSN_peer - CSN_current| ≤ CSN_current × 0.15
   用途: 找到使用强度相似的器材，不限维修阶段

3. 仅维修次数匹配:
   条件: 【改进】智能分层匹配策略
   用途: 找到维修阶段相似的器材，不限使用强度

【改进】智能维修次数匹配策略:
基于数据分布(87.6%≤3次维修)的分层匹配:
  • 首次维修层: [1] 次维修互相匹配
  • 早期维修层: [2, 3] 次维修互相匹配
  • 中期维修层: [4, 5, 6] 次维修互相匹配
  • 后期维修层: [7, 8, 9, 10, 11, 12, 13, 14] 次维修互相匹配
  • 层内精确匹配，层间不互通，避免维修阶段混合
  • 解决了87.6%数据集中在≤3次维修导致的匹配过宽问题

统计特征计算:
- 对每种匹配策略找到的同伴器材TSR值计算:
  • 平均值 (avg): 反映同伴群体的典型表现
  • 中位数 (median): 抗异常值的中心趋势
  • 标准差 (std): 反映同伴群体的一致性
  • 数量 (count): 反映相似性匹配的丰富度

质量控制机制:
- 最小样本控制: 同伴数量≥2才计算统计特征
- 件号内异常值检测: 使用Z-score>2标识件号内的异常器材
- 【新增】同伴族异常值检测和排除:
  • 检测方法: IQR方法
  • IQR因子: 1.5
  • 最小检测样本数: 4
  • 最大异常值比例: 30.0%
  • 统计特征基于排除异常值后的清洁数据计算
- 相似度评分: 找到的同伴数/总可能同伴数
- 缺失值处理: 无同伴的器材特征值填充为0

6. 业务价值与应用
==================================================
同伴特征的业务价值:
1. 解决小样本问题: 利用相似器材的历史数据补充信息
2. 提升预测精度: 群体智慧辅助个体预测
3. 增强可解释性: 预测结果可追溯到具体同伴案例
4. 支持决策制定: 为维修计划提供群体经验参考

应用场景:
- 新器材TSR预测: 利用同型号器材的历史表现
- 维修决策支持: 参考相似器材的维修效果
- 风险评估分析: 通过同伴表现差异评估不确定性
- 库存管理优化: 基于群体可靠性模式制定库存策略

================================================================================
报告结束 - 详细解释了同伴特征的完整计算过程
================================================================================
