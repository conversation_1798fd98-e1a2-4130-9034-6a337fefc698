{"model_name": "增强特征-梯度提升", "model_type": "GradientBoostingRegressor", "model_parameters": {"alpha": 0.9, "ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "squared_error", "max_depth": 5, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 150, "n_iter_no_change": null, "random_state": 42, "subsample": 0.8, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}, "feature_count": 26, "train_size": 699, "val_size": 234, "test_size": 234, "training_timestamp": "20250813_201958"}