================================================================================
                    模型拟合和验证详细报告
                 Detailed Model Fitting & Validation Report
================================================================================

报告生成时间: 20250813_202040
模型数量: 5

1. 执行摘要
--------------------------------------------------
最佳模型: 增强特征-梯度提升
最佳R²: 0.9177
最佳MAE: 611.7
交叉验证稳定性: 0.0148

2. 详细模型分析
--------------------------------------------------

模型: 传统特征-梯度提升
------------------------------
性能指标:
  测试集R²: -0.0504
  训练集R²: 0.9296
  过拟合程度: 0.9800
  MAE: 4538.4
  RMSE: 6029.9
  MAPE: 10760.4%
  交叉验证: -0.0826 ± 0.0796
误差分析:
  25%分位误差: 1615.2
  50%分位误差: 3458.7
  75%分位误差: 6133.4
  95%分位误差: 13225.7
  最大误差: 17829.8
前5重要特征:
  1. CSN (传统): 0.2147
  2. TSN (传统): 0.1815
  3. tsn_csn_ratio (传统): 0.1783
  4. since_first_tsn (传统): 0.1405
  5. log_price (传统): 0.0848
模型诊断:
  ⚠️  检测到过拟合风险
  ✅ 交叉验证稳定

模型: 增强特征-梯度提升
------------------------------
性能指标:
  测试集R²: 0.9177
  训练集R²: 0.9999
  过拟合程度: 0.0822
  MAE: 611.7
  RMSE: 1687.4
  MAPE: 95.1%
  交叉验证: 0.9364 ± 0.0148
误差分析:
  25%分位误差: 50.7
  50%分位误差: 134.6
  75%分位误差: 443.1
  95%分位误差: 2410.7
  最大误差: 15882.2
前5重要特征:
  1. cost_per_tsr (传统): 0.5721
  2. reliability_index (传统): 0.2650
  3. log_price (传统): 0.0624
  4. CSN (传统): 0.0244
  5. TSN (传统): 0.0156
模型诊断:
  ✅ 拟合程度良好
  ✅ 交叉验证稳定

模型: 传统特征-随机森林
------------------------------
性能指标:
  测试集R²: 0.0832
  训练集R²: 0.5504
  过拟合程度: 0.4671
  MAE: 4266.2
  RMSE: 5633.3
  MAPE: 10458.7%
  交叉验证: 0.0665 ± 0.0696
误差分析:
  25%分位误差: 1795.6
  50%分位误差: 3219.8
  75%分位误差: 5648.8
  95%分位误差: 12546.9
  最大误差: 17961.6
前5重要特征:
  1. CSN (传统): 0.2523
  2. tsn_csn_ratio (传统): 0.1714
  3. TSN (传统): 0.1689
  4. since_first_tsn (传统): 0.1224
  5. cumulative_repair_intensity (传统): 0.0795
模型诊断:
  ⚠️  检测到过拟合风险
  ✅ 交叉验证稳定

模型: 增强特征-随机森林
------------------------------
性能指标:
  测试集R²: 0.9009
  训练集R²: 0.9768
  过拟合程度: 0.0759
  MAE: 802.1
  RMSE: 1852.5
  MAPE: 65.2%
  交叉验证: 0.8982 ± 0.0139
误差分析:
  25%分位误差: 54.3
  50%分位误差: 168.7
  75%分位误差: 614.4
  95%分位误差: 4001.5
  最大误差: 11715.7
前5重要特征:
  1. cost_per_tsr (传统): 0.5015
  2. reliability_index (传统): 0.3559
  3. log_price (传统): 0.0356
  4. CSN (传统): 0.0245
  5. cumulative_repair_intensity (传统): 0.0188
模型诊断:
  ✅ 拟合程度良好
  ✅ 交叉验证稳定

模型: 线性回归基线
------------------------------
性能指标:
  测试集R²: 0.1913
  训练集R²: 0.2185
  过拟合程度: 0.0272
  MAE: 3954.7
  RMSE: 5290.8
  MAPE: 6154.5%
  交叉验证: -11352748.3932 ± 22705497.0179
误差分析:
  25%分位误差: 1373.6
  50%分位误差: 2894.9
  75%分位误差: 5386.2
  95%分位误差: 11722.8
  最大误差: 18025.5
前5重要特征:
  1. peer_similarity_score (同伴): 13485.5575
  2. is_frequent_repair (传统): 3440.4383
  3. repair_degradation (传统): 3139.8430
  4. repair_ratio (传统): 3078.6164
  5. tsn_csn_ratio (传统): 1251.5741
模型诊断:
  ✅ 拟合程度良好
  ⚠️  交叉验证不稳定

3. 模型对比分析
--------------------------------------------------
R²性能排名:
  1. 增强特征-梯度提升: 0.9177
  2. 增强特征-随机森林: 0.9009
  3. 线性回归基线: 0.1913
  4. 传统特征-随机森林: 0.0832
  5. 传统特征-梯度提升: -0.0504

MAE排名 (越小越好):
  1. 增强特征-梯度提升: 611.7
  2. 增强特征-随机森林: 802.1
  3. 线性回归基线: 3954.7
  4. 传统特征-随机森林: 4266.2
  5. 传统特征-梯度提升: 4538.4

4. 特征工程效果分析
--------------------------------------------------
传统特征平均R²: 0.0164
增强特征平均R²: 0.9093
特征工程提升: 0.8929 (5442.6%)
结论: 同伴特征显著提升了模型性能 ✅

5. 建议和后续步骤
--------------------------------------------------
模型选择建议:
- 推荐使用: 增强特征-梯度提升
- 性能指标: R²=0.9177, MAE=611.7

改进建议:
- 收集更多训练数据以提高模型稳定性
- 考虑集成学习方法结合多个模型
- 探索深度学习方法处理复杂特征交互
- 定期重新训练模型以适应数据分布变化

部署建议:
- 建立模型监控机制跟踪预测质量
- 设置预测误差阈值报警
- 定期评估模型性能并更新
- 考虑A/B测试验证新模型效果
