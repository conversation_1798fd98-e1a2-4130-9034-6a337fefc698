================================================================================
                    按件号独立建模综合分析报告
             Comprehensive Part-wise Modeling Analysis Report
================================================================================

分析时间: 2025-08-13 20:19:23
总件号数: 6
成功建模件号数: 5
跳过件号数: 1
建模成功率: 83.3%

1. 模型性能排名 (Model Performance Ranking)
--------------------------------------------------
排名   件号           样本数      最佳模型            测试R²       测试MAE       
-----------------------------------------------------------------
1    <USER>      <GROUP>      GradientBoosting 0.9694     700.63      
2    1706903      347      RandomForest    0.9206     671.26      
3    761574B      792      GradientBoosting 0.9159     806.62      
4    740119H      271      GradientBoosting 0.8768     1185.07     
5    740119G      86       GradientBoosting 0.8761     1304.89     

2. 统计分析 (Statistical Analysis)
--------------------------------------------------
平均测试R²: 0.9118
R²标准差: 0.0384
最佳R²: 0.9694 (件号: 752168B)
R² > 0.8的件号数: 5
R² > 0.6的件号数: 5

3. 最佳模型类型分布 (Best Model Type Distribution)
--------------------------------------------------
GradientBoosting: 4 (80.0%)
RandomForest: 1 (20.0%)

4. 跳过件号分析 (Skipped Parts Analysis)
--------------------------------------------------
insufficient_data: 1 个件号

数据不足的件号详情:
  752168C: 8 条记录

5. 建议 (Recommendations)
--------------------------------------------------
✅ 优先部署R²>0.8的件号模型
⚠️  R²在0.6-0.8的件号可以部署但需要监控
❌ R²<0.6的件号需要更多数据或特征改进
📊 数据不足的件号可以考虑迁移学习或集成方法
🔄 建议定期重新训练以适应新数据
