{"successful_parts": {"740119G": {"model_count": 3, "data_count": 86, "best_model": "特征增强梯度提升", "best_test_r2": 0.8761148580233652, "output_dir": "modeloutput\\件号级模型分析_20250813_202040\\件号_740119G"}, "761574B": {"model_count": 3, "data_count": 792, "best_model": "特征增强梯度提升", "best_test_r2": 0.9158766276377994, "output_dir": "modeloutput\\件号级模型分析_20250813_202040\\件号_761574B"}, "740119H": {"model_count": 3, "data_count": 271, "best_model": "特征增强梯度提升", "best_test_r2": 0.8768286114092838, "output_dir": "modeloutput\\件号级模型分析_20250813_202040\\件号_740119H"}, "752168B": {"model_count": 3, "data_count": 177, "best_model": "特征增强梯度提升", "best_test_r2": 0.9693786791117127, "output_dir": "modeloutput\\件号级模型分析_20250813_202040\\件号_752168B"}, "1706903": {"model_count": 3, "data_count": 347, "best_model": "特征增强随机森林", "best_test_r2": 0.9205628327073653, "output_dir": "modeloutput\\件号级模型分析_20250813_202040\\件号_1706903"}}, "failed_parts": {"752168C": {"reason": "insufficient_data", "data_count": 8}}, "overall_statistics": {"total_parts": 6, "successful_parts": 5, "failed_parts": 1, "success_rate": 0.8333333333333334, "timestamp": "20250813_202040", "output_directory": "modeloutput\\件号级模型分析_20250813_202040"}, "model_comparison": {}}