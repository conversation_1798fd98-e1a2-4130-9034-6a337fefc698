# -*- coding: utf-8 -*-
"""
按件号处理的TSR预测与置信度分析系统
使用comprehensive_tsr_analyzer的输出参数，按件号逐个处理

功能流程：
1. 使用comprehensive_tsr_analyzer.py计算件号、序号、预测TSR
2. 按件号逐个处理，对每个件号内的不同序号进行计算
3. 使用件序号相似性分析建立KM生存曲线（关闭可视化，保留数据）
4. 基于预测TSR从生存曲线中读取置信度
5. 统计各件序号的生存曲线数据量和相似件序号数目
6. 输出最终结果：件号、序号、预测TSR、置信度、模型版本

【KM生存曲线样本量建议】
- <5个数据点：极不可靠，仅供趋势参考
- 5-10个数据点：可初步看趋势，置信区间较宽
- 10-20个数据点：较为平滑，适合一般分析
- >20个数据点：较为稳定，统计意义较强
- 工程建议：每条KM曲线至少10个及以上数据点
- 若样本量极小，建议在表格和日志中警告
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
import matplotlib.pyplot as plt
import seaborn as sns
from lifelines import KaplanMeierFitter
import warnings
import traceback
import re
warnings.filterwarnings('ignore')

# 导入comprehensive_tsr_analyzer模块
sys.path.append(r"C:\Users\<USER>\Desktop\中航材\codes\git_project\air-ml-main")
try:
    from comprehensive_tsr_analyzer import ComprehensiveTSRAnalyzer, BusinessRuleManager
    print("✅ 成功导入comprehensive_tsr_analyzer模块")
except ImportError as e:
    print(f"❌ 导入comprehensive_tsr_analyzer模块失败: {e}")
    sys.exit(1)

# DTW库导入
try:
    from dtaidistance import dtw
    DTW_AVAILABLE = True
    print("✅ DTW库可用")
except ImportError:
    print("⚠️ DTW库未安装，将使用简化DTW实现")
    DTW_AVAILABLE = False

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# =============================================================================
# 配置参数
# =============================================================================
INPUT_FILE_PATH = r"飞机数据/送修数据_新增平均TSR.xlsx"
OUTPUT_BASE_PATH = r"modeloutput"
# 额外：外部TSR预测文件默认路径（用于跳过步骤1时从文件读取预测TSR）
EXTERNAL_TSR_PREDICTION_FILE_DEFAULT = r"飞机数据/最后维修TSR预测结果.xlsx"
MODEL_VERSION = "V1.0"

# 新增超参数
TSR_PREDICTION_PERCENTILE = 0.5  # TSR预测分位数，默认50%分位数，可调整到60%等
SURVIVAL_CURVE_DATA_THRESHOLD = 10  # 生存曲线数据量阈值，小于此值使用件号级KM生存曲线
SIMILARITY_THRESHOLD = 0.7       # 相似性阈值
TOP_SIMILAR_COUNT = 10           # 最大相似序号数量
SAVE_SIMILARITY_RESULTS = True     # 是否保存相似度分析过程文件
SIMILARITY_THRESHOLD_FOR_SAVING = 0.7 # 用于保存的相似度阈值

# 数据清洗规则（可通过构造参数覆盖）
DEFAULT_CLEANING_RULES = {
    'exclude_group_abnormal_types': ['件退出'],
    'exclude_single_record_abnormal_types': ['未修好', '合同执行中', '未检测出故障']
}


# 开关：是否执行步骤1（建模+预测TSR）。设为False时，从外部文件读取预测TSR
ENABLE_STEP1_PREDICTION = False

# 特征权重配置
FEATURE_WEIGHTS = {
    'TSN': 0.3,
    'TSR': 0.1,
    'CSN': 0.1,
    '报价_归一化': 0.3,
    'repair_sequence': 0.2
}

class PartBasedTSRConfidenceAnalyzer:
    """按件号处理的TSR预测与置信度分析器"""
    
    def __init__(self, tsr_percentile=TSR_PREDICTION_PERCENTILE, 
                 survival_curve_data_threshold=SURVIVAL_CURVE_DATA_THRESHOLD,
                 similarity_threshold=SIMILARITY_THRESHOLD,
                 top_similar_count=TOP_SIMILAR_COUNT,
                 repair_history_file=INPUT_FILE_PATH,
                 enable_step1_prediction=ENABLE_STEP1_PREDICTION,
                 external_prediction_file=EXTERNAL_TSR_PREDICTION_FILE_DEFAULT,
                 data_cleaning_rules=None):
        self.rule_manager = BusinessRuleManager()
        self.tsr_analyzer = ComprehensiveTSRAnalyzer(self.rule_manager)
        self.output_dir = None
        self.tsr_predictions = None
        self.survival_curves = {}
        self.debug_log = []
        self.survival_statistics = {}  # 生存曲线统计信息
        self.part_processing_results = {}  # 按件号处理结果
        self.part_level_km_curves = {}  # 件号级KM生存曲线
        self.all_similarity_results = [] # 存储所有高于阈值的相似度记录
        
        # 超参数
        self.tsr_percentile = tsr_percentile
        self.survival_curve_data_threshold = survival_curve_data_threshold
        self.similarity_threshold = similarity_threshold
        self.top_similar_count = top_similar_count
        
        # 数据文件路径
        self.repair_history_file = repair_history_file
        # 新增：控制是否执行步骤1预测，及外部预测文件路径
        self.enable_step1_prediction = enable_step1_prediction
        self.external_prediction_file = external_prediction_file
        
        # 数据清洗规则
        self.data_cleaning_rules = data_cleaning_rules or DEFAULT_CLEANING_RULES
        
        self.log_debug(f"初始化超参数:")
        self.log_debug(f"  TSR预测分位数: {self.tsr_percentile:.1%}")
        self.log_debug(f"  生存曲线数据量阈值: {self.survival_curve_data_threshold}")
        self.log_debug(f"  相似性阈值: {self.similarity_threshold}")
        self.log_debug(f"  最大相似序号数: {self.top_similar_count}")
        self.log_debug(f"  维修历史文件: {self.repair_history_file}")
        self.log_debug(f"  是否执行步骤1预测: {self.enable_step1_prediction}")
        self.log_debug(f"  外部预测文件: {self.external_prediction_file}")
        
    def log_debug(self, message, level="INFO"):
        """记录调试日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.debug_log.append(log_entry)
        
    def create_output_directory(self):
        """创建带时间戳的输出目录"""
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        self.output_dir = os.path.join(OUTPUT_BASE_PATH, f"按件号TSR置信度分析_{timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 创建子文件夹
        self.survival_data_dir = os.path.join(self.output_dir, 'survival_data')
        self.prediction_dir = os.path.join(self.output_dir, 'predictions')
        self.debug_dir = os.path.join(self.output_dir, 'debug_logs')
        self.statistics_dir = os.path.join(self.output_dir, 'statistics')
        self.part_results_dir = os.path.join(self.output_dir, 'part_results')
        
        os.makedirs(self.survival_data_dir, exist_ok=True)
        os.makedirs(self.prediction_dir, exist_ok=True)
        os.makedirs(self.debug_dir, exist_ok=True)
        os.makedirs(self.statistics_dir, exist_ok=True)
        os.makedirs(self.part_results_dir, exist_ok=True)
        
        self.log_debug(f"输出目录创建完成: {self.output_dir}")
        return self.output_dir
    
    def step1_predict_tsr(self, df):
        """步骤1：使用comprehensive_tsr_analyzer预测TSR"""
        self.log_debug("=== 步骤1：TSR预测 ===", "STEP")
        
        try:
            # 数据处理
            self.log_debug("开始处理数据...")
            # 数据清洗（根据规则剔除异常记录）
            df_clean = self.apply_input_cleaning_rules(df)
            self.log_debug(f"清洗后输入数据: {len(df_clean)} / 原始 {len(df)} 条")

            processed_df = self.tsr_analyzer.process_data(df_clean)
            self.log_debug(f"数据处理完成，共 {len(processed_df)} 条记录")
            
            # 按件号分析和建模
            self.log_debug("开始按件号建立模型...")
            part_results, part_stats, successful_models, skipped_parts = \
                self.tsr_analyzer.analyze_by_part_number_comprehensive(processed_df, self.prediction_dir)
            
            self.log_debug(f"模型建立完成: 成功{len(successful_models)}个，跳过{len(skipped_parts)}个")
            
            # TSR预测
            self.log_debug("开始执行TSR预测...")
            prediction_results = self.tsr_analyzer.predict_future_tsr(part_results, processed_df, self.prediction_dir)
            
            # 读取预测结果
            prediction_file = os.path.join(self.prediction_dir, "最后维修TSR预测结果.xlsx")
            if os.path.exists(prediction_file):
                self.tsr_predictions = pd.read_excel(prediction_file, sheet_name='预测结果')
                self.log_debug(f"✅ 成功预测 {len(self.tsr_predictions)} 个序号的TSR")
                self.log_debug(f"✅ 预测结果已保存至: {prediction_file}")
                
                # 保存调试信息
                debug_file = os.path.join(self.debug_dir, "tsr_predictions_debug.xlsx")
                with pd.ExcelWriter(debug_file, engine='openpyxl') as writer:
                    self.tsr_predictions.to_excel(writer, sheet_name='TSR预测结果', index=False)
                    
                    # 添加统计信息
                    stats_df = pd.DataFrame({
                        '统计项': ['总序号数', '有预测TSR的序号数', '平均预测TSR', '预测TSR范围'],
                        '数值': [
                            len(self.tsr_predictions),
                            len(self.tsr_predictions[self.tsr_predictions['预测TSR'].notna()]),
                            self.tsr_predictions['预测TSR'].mean(),
                            f"{self.tsr_predictions['预测TSR'].min():.1f} - {self.tsr_predictions['预测TSR'].max():.1f}"
                        ]
                    })
                    stats_df.to_excel(writer, sheet_name='预测统计', index=False)
                
                self.log_debug(f"调试信息已保存至: {debug_file}")
                
            else:
                raise FileNotFoundError("TSR预测结果文件未找到")
            
            return processed_df, part_results
            
        except Exception as e:
            self.log_debug(f"❌ TSR预测步骤失败: {e}", "ERROR")
            self.log_debug(f"错误详情: {traceback.format_exc()}", "ERROR")
            raise

    def normalize_price_data_globally(self, df):
        """对整个数据集的报价进行全局归一化"""
        self.log_debug("开始全局归一化报价数据...")
        
        if '报价' not in df.columns:
            self.log_debug("⚠️ 数据中不包含报价列", "WARNING")
            return df
        
        price_data = df['报价'].dropna()
        if len(price_data) == 0:
            self.log_debug("⚠️ 报价列无有效数据", "WARNING")
            return df
        
        price_min = price_data.min()
        price_max = price_data.max()
        
        if price_max > price_min:
            df['报价_归一化'] = df['报价'].apply(
                lambda x: (x - price_min) / (price_max - price_min) if pd.notna(x) else np.nan
            )
            self.log_debug(f"✅ 全局报价归一化完成: [{price_min:.0f}, {price_max:.0f}] -> [0.0, 1.0]")
        else:
            df['报价_归一化'] = df['报价']
            self.log_debug("⚠️ 报价数据无变异，复制原始数据", "WARNING")
        
        return df

    def calculate_simple_similarity(self, target_data, candidate_data):
        """简化的相似性计算"""
        try:
            # 计算TSN相似性
            target_tsn_mean = target_data['TSN'].mean()
            candidate_tsn_mean = candidate_data['TSN'].mean()
            
            if max(target_tsn_mean, candidate_tsn_mean) > 0:
                tsn_similarity = 1 - abs(target_tsn_mean - candidate_tsn_mean) / max(target_tsn_mean, candidate_tsn_mean)
            else:
                tsn_similarity = 1.0
            
            # 计算维修次数相似性
            target_repair_count = len(target_data)
            candidate_repair_count = len(candidate_data)
            
            if max(target_repair_count, candidate_repair_count) > 0:
                repair_similarity = 1 - abs(target_repair_count - candidate_repair_count) / max(target_repair_count, candidate_repair_count)
            else:
                repair_similarity = 1.0
            
            # 综合相似性
            overall_similarity = 0.7 * tsn_similarity + 0.3 * repair_similarity
            
            return overall_similarity
            
        except Exception as e:
            self.log_debug(f"相似性计算异常: {e}", "ERROR")
            return 0.0

    def build_part_level_km_curve(self, part_num, part_df):
        """建立件号级KM生存曲线"""
        try:
            self.log_debug(f"开始建立件号 {part_num} 的件号级KM生存曲线...")
            
            # 准备生存分析数据
            survival_data = []
            
            # 遍历所有序号，每个序号的最后一次维修作为censored数据，其他作为事件数据
            for serial in part_df['序号'].unique():
                serial_data = part_df[part_df['序号'] == serial].sort_values('第几次维修')
                
                for i, (_, row) in enumerate(serial_data.iterrows()):
                    tsr_time = row.get('TSR', row.get('平均TSR', 0)) if pd.notna(row.get('TSR', row.get('平均TSR', 0))) else 0
                    is_last_repair = (i == len(serial_data) - 1)
                    event_occurred = 0 if is_last_repair else 1
                    
                    survival_data.append({
                        '序号': serial,
                        '生存时间_TSR': tsr_time,
                        '事件发生': event_occurred,
                        '权重': 1.0
                    })
            
            if not survival_data:
                self.log_debug(f"件号 {part_num} 无生存数据", "WARNING")
                return None
            
            survival_df = pd.DataFrame(survival_data)
            self.log_debug(f"件号 {part_num} 总数据点: {len(survival_df)}")
            
            # 建立KM生存曲线
            kmf = KaplanMeierFitter()
            kmf.fit(survival_df['生存时间_TSR'], survival_df['事件发生'])
            
            # 保存生存曲线数据
            part_level_curve_data = {
                'kmf': kmf,
                'survival_data': survival_df,
                'part_num': part_num,
                'total_data_points': len(survival_df),
                'unique_serials': len(survival_df['序号'].unique())
            }
            
            self.log_debug(f"✅ 件号 {part_num} 件号级生存曲线已建立，数据点: {len(survival_df)}")
            
            return part_level_curve_data
            
        except Exception as e:
            self.log_debug(f"建立件号级生存曲线失败: {e}", "ERROR")
            return None

    def find_similar_serials(self, target_data, part_df, target_serial, top_n=None, similarity_threshold=None):
        """为目标序号找到最相似的序号（带相似性阈值过滤）"""
        # 使用实例超参数作为默认值
        if top_n is None:
            top_n = self.top_similar_count
        if similarity_threshold is None:
            similarity_threshold = self.similarity_threshold
            
        try:
            target_part_num = target_data['件号'].iloc[0]
            serials = part_df['序号'].unique()
            serials = [s for s in serials if s != target_serial]  # 排除自己
            
            self.log_debug(f"候选序号数量: {len(serials)}")
            
            similarities = []
            
            for i, candidate_serial in enumerate(serials[:50]):  # 增加候选数量
                candidate_data = part_df[part_df['序号'] == candidate_serial].sort_values('第几次维修')
                
                if len(candidate_data) == 0:
                    continue
                
                # 计算相似性
                similarity_score = self.calculate_simple_similarity(target_data, candidate_data)
                
                # 如果启用了保存功能，并且相似度高于保存阈值，则记录
                if SAVE_SIMILARITY_RESULTS and similarity_score >= SIMILARITY_THRESHOLD_FOR_SAVING:
                    self.all_similarity_results.append({
                        '目标件号': target_part_num,
                        '目标序号': target_serial,
                        '备选件号': candidate_data['件号'].iloc[0],
                        '备选序号': candidate_serial,
                        '相似度': similarity_score
                    })

                # 只保留达到建模阈值的相似序号
                if similarity_score >= similarity_threshold:
                    similarities.append((candidate_serial, similarity_score))
                
                if i % 10 == 0:
                    self.log_debug(f"已计算 {i+1}/{min(50, len(serials))} 个候选序号")
            
            # 按相似性排序，取前top_n个
            similarities.sort(key=lambda x: x[1], reverse=True)
            similar_serials = similarities[:top_n]
            
            if len(similar_serials) > 0:
                self.log_debug(f"最相似序号: {[s[0] for s in similar_serials]}")
                self.log_debug(f"相似度范围: {similar_serials[0][1]:.3f} - {similar_serials[-1][1]:.3f}")
                self.log_debug(f"达到阈值(≥{similarity_threshold})的序号数: {len(similar_serials)}")
            else:
                self.log_debug(f"⚠️ 没有序号达到相似性阈值 {similarity_threshold}")
            
            return similar_serials
            
        except Exception as e:
            self.log_debug(f"相似序号查找失败: {e}", "ERROR")
            return []

    def build_weighted_km_curve_no_plot(self, target_serial, target_data, similar_serials, part_df):
        """建立加权的KM生存曲线（不生成可视化，只保留数据）"""
        try:
            self.log_debug(f"开始建立序号 {target_serial} 的加权KM生存曲线...")
            
            # 准备生存分析数据
            survival_data = []
            
            # 添加目标序号数据（权重为1.0）
            for i, (_, row) in enumerate(target_data.iterrows()):
                tsr_time = row.get('TSR', row.get('平均TSR', 0)) if pd.notna(row.get('TSR', row.get('平均TSR', 0))) else 0
                is_last_repair = (i == len(target_data) - 1)
                event_occurred = 0 if is_last_repair else 1
                
                survival_data.append({
                    '序号': target_serial,
                    '生存时间_TSR': tsr_time,
                    '事件发生': event_occurred,
                    '相似度权重': 1.0,
                    '序号类型': '目标序号'
                })
            
            self.log_debug(f"目标序号数据点: {len(target_data)}")
            
            # 添加相似序号数据
            for serial, similarity_score in similar_serials:
                serial_data = part_df[part_df['序号'] == serial].sort_values('第几次维修')
                
                for i, (_, row) in enumerate(serial_data.iterrows()):
                    tsr_time = row.get('TSR', row.get('平均TSR', 0)) if pd.notna(row.get('TSR', row.get('平均TSR', 0))) else 0
                    is_last_repair = (i == len(serial_data) - 1)
                    event_occurred = 0 if is_last_repair else 1
                    
                    survival_data.append({
                        '序号': serial,
                        '生存时间_TSR': tsr_time,
                        '事件发生': event_occurred,
                        '相似度权重': similarity_score,
                        '序号类型': '相似序号'
                    })
            
            self.log_debug(f"相似序号数据点: {len(survival_data) - len(target_data)}")
            
            if not survival_data:
                self.log_debug("无生存数据", "WARNING")
                return None
            
            survival_df = pd.DataFrame(survival_data)
            self.log_debug(f"总数据点: {len(survival_df)}")
            
            # 建立加权KM生存曲线
            kmf = KaplanMeierFitter()
            kmf.fit(survival_df['生存时间_TSR'], survival_df['事件发生'], 
                   weights=survival_df['相似度权重'])
            
            # 保存生存曲线数据（不生成可视化）
            survival_curve_data = {
                'kmf': kmf,
                'survival_data': survival_df,
                'target_serial': target_serial,
                'similar_serials': similar_serials,
                'total_data_points': len(survival_df),
                'target_data_points': len(target_data),
                'similar_data_points': len(survival_data) - len(target_data)
            }
            
            self.log_debug(f"✅ 序号 {target_serial} 生存曲线已建立，数据点: {len(survival_df)}")
            
            return survival_curve_data
            
        except Exception as e:
            self.log_debug(f"建立生存曲线失败: {e}", "ERROR")
            return None

    def step2_build_survival_curves_by_part(self, processed_df):
        """步骤2：按件号逐个建立生存曲线"""
        self.log_debug("=== 步骤2：按件号建立生存曲线 ===", "STEP")
        
        if self.tsr_predictions is None:
            self.log_debug("❌ TSR预测结果为空，无法建立生存曲线", "ERROR")
            return
        
        # 数据预处理
        self.log_debug("开始数据预处理...")
        processed_df = self.normalize_price_data_globally(processed_df)
        
        # 按件号分组处理
        part_numbers = self.tsr_predictions['件号'].unique()
        self.log_debug(f"需要处理的件号数: {len(part_numbers)}")
        
        total_successful_curves = 0
        total_failed_curves = 0
        
        for part_idx, part_num in enumerate(part_numbers, 1):
            self.log_debug(f"处理件号 {part_num} ({part_idx}/{len(part_numbers)})")
            
            # 获取该件号的所有序号（修复类型问题）
            part_serials = self.tsr_predictions[self.tsr_predictions['件号'] == part_num]['序号'].astype(str).unique()
            self.log_debug(f"件号 {part_num} 包含 {len(part_serials)} 个序号")
            
            # 获取该件号的所有数据
            part_df = processed_df[processed_df['件号'] == part_num].copy()
            
            # 建立件号级KM生存曲线（用于相似序号不足时的fallback）
            part_level_km = self.build_part_level_km_curve(part_num, part_df)
            if part_level_km is not None:
                self.part_level_km_curves[part_num] = part_level_km
                self.log_debug(f"件号 {part_num} 建立件号级KM曲线成功，数据量: {part_level_km['total_data_points']}")
            else:
                self.log_debug(f"件号 {part_num} 建立件号级KM曲线失败")
            
            part_successful = 0
            part_failed = 0
            # 统计该件号的总历史维修次数
            part_total_repairs = len(part_df)
            part_unique_serials = part_df['序号'].nunique()
            part_avg_repairs_per_serial = part_total_repairs / part_unique_serials if part_unique_serials > 0 else 0
            
            part_survival_stats = {
                'total_serials': len(part_serials),
                'successful_curves': 0,
                'failed_curves': 0,
                'total_survival_data': 0,
                'similar_serials_count': 0,
                'total_repairs': part_total_repairs,
                'unique_serials_in_part': part_unique_serials,
                'avg_repairs_per_serial': part_avg_repairs_per_serial,
                'serial_details': []
            }
            
            for serial_idx, serial in enumerate(part_serials, 1):
                try:
                    self.log_debug(f"处理序号 {serial} ({serial_idx}/{len(part_serials)})")
                    
                    # 获取该序号的数据
                    serial_data = part_df[part_df['序号'] == serial].copy()
                    if len(serial_data) == 0:
                        self.log_debug(f"序号 {serial} 无数据，尝试使用件号级KM曲线")
                        # 尝试使用件号级KM曲线
                        if part_num in self.part_level_km_curves:
                            part_level_curve = self.part_level_km_curves[part_num]
                            survival_curve = {
                                'kmf': part_level_curve['kmf'],
                                'survival_data': part_level_curve['survival_data'],
                                'target_serial': serial,
                                'similar_serials': [],
                                'total_data_points': part_level_curve['total_data_points'],
                                'target_data_points': 0,
                                'similar_data_points': 0,
                                'use_part_level': True
                            }
                            self.survival_curves[serial] = survival_curve
                            part_successful += 1
                            survival_data_count = survival_curve['total_data_points']
                            part_survival_stats['total_survival_data'] += survival_data_count
                            part_survival_stats['serial_details'].append({
                                'serial': serial,
                                'status': 'part_level_km_no_data',
                                'survival_data_count': survival_data_count,
                                'similar_serials': 0
                            })
                            self.log_debug(f"✅ 序号无数据，使用件号级KM曲线，数据量: {survival_data_count}")
                        else:
                            part_failed += 1
                            part_survival_stats['serial_details'].append({
                                'serial': serial,
                                'status': 'no_data',
                                'survival_data_count': 0,
                                'similar_serials': 0
                            })
                            self.log_debug(f"❌ 序号 {serial} 无数据且件号级KM曲线不可用")
                        continue
                    
                    # 寻找相似序号
                    similar_serials = self.find_similar_serials(serial_data, part_df, serial)
                    
                    # 先尝试建立序号级生存曲线
                    if len(similar_serials) == 0:
                        if len(serial_data) > 1:
                            self.log_debug(f"无相似序号，但本序号有{len(serial_data)}次维修，尝试仅用本序号数据建立生存曲线")
                            survival_curve = self.build_weighted_km_curve_no_plot(serial, serial_data, [], part_df)
                            if survival_curve is not None:
                                survival_data_count = survival_curve['total_data_points']
                                
                                # 检查生存曲线数据量是否达到阈值
                                if survival_data_count < self.survival_curve_data_threshold:
                                    self.log_debug(f"自身生存曲线数据量({survival_data_count}) < 阈值({self.survival_curve_data_threshold})，尝试使用件号级KM曲线")
                                    
                                    # 尝试使用件号级KM生存曲线
                                    if part_num in self.part_level_km_curves:
                                        part_level_curve = self.part_level_km_curves[part_num]
                                        part_level_data_count = part_level_curve['total_data_points']
                                        
                                        if part_level_data_count > survival_data_count:
                                            # 件号级数据量更多，使用件号级生存曲线
                                            survival_curve = {
                                                'kmf': part_level_curve['kmf'],
                                                'survival_data': part_level_curve['survival_data'],
                                                'target_serial': serial,
                                                'similar_serials': [],
                                                'total_data_points': part_level_curve['total_data_points'],
                                                'target_data_points': len(serial_data),
                                                'similar_data_points': 0,
                                                'use_part_level': True
                                            }
                                            survival_data_count = part_level_data_count
                                            self.log_debug(f"✅ 使用件号级KM曲线替代自身数据，数据量提升: {survival_data_count}")
                                            status = 'part_level_km_from_self'
                                        else:
                                            self.log_debug(f"件号级数据量({part_level_data_count})不比自身数据量大，保持自身曲线")
                                            status = 'self_only_low_data'
                                    else:
                                        self.log_debug(f"件号级KM曲线不可用，保持自身曲线")
                                        status = 'self_only_low_data'
                                else:
                                    status = 'self_only_success'
                                
                                self.survival_curves[serial] = survival_curve
                                part_successful += 1
                                part_survival_stats['total_survival_data'] += survival_data_count
                                part_survival_stats['serial_details'].append({
                                    'serial': serial,
                                    'status': status,
                                    'survival_data_count': survival_data_count,
                                    'similar_serials': 0
                                })
                                self.log_debug(f"✅ 仅用本序号数据建立生存曲线成功，数据量: {survival_data_count}")
                            else:
                                # 自身数据建立生存曲线失败，尝试使用件号级KM曲线
                                if part_num in self.part_level_km_curves:
                                    self.log_debug(f"自身数据生存曲线失败，尝试使用件号级KM曲线")
                                    part_level_curve = self.part_level_km_curves[part_num]
                                    survival_curve = {
                                        'kmf': part_level_curve['kmf'],
                                        'survival_data': part_level_curve['survival_data'],
                                        'target_serial': serial,
                                        'similar_serials': [],
                                        'total_data_points': part_level_curve['total_data_points'],
                                        'target_data_points': len(serial_data),
                                        'similar_data_points': 0,
                                        'use_part_level': True
                                    }
                                    self.survival_curves[serial] = survival_curve
                                    part_successful += 1
                                    survival_data_count = survival_curve['total_data_points']
                                    part_survival_stats['total_survival_data'] += survival_data_count
                                    part_survival_stats['serial_details'].append({
                                        'serial': serial,
                                        'status': 'fallback_part_level_from_self',
                                        'survival_data_count': survival_data_count,
                                        'similar_serials': 0
                                    })
                                    self.log_debug(f"✅ 使用件号级KM曲线作为fallback，数据量: {survival_data_count}")
                                else:
                                    part_failed += 1
                                    part_survival_stats['serial_details'].append({
                                        'serial': serial,
                                        'status': 'curve_failed',
                                        'survival_data_count': 0,
                                        'similar_serials': 0
                                    })
                                    self.log_debug(f"❌ 仅用本序号数据建立生存曲线失败，件号级KM曲线也不可用")
                            continue
                        else:
                            self.log_debug(f"序号 {serial} 无相似序号且自身维修次数不足，尝试使用件号级KM曲线")
                            # 尝试使用件号级KM曲线
                            if part_num in self.part_level_km_curves:
                                part_level_curve = self.part_level_km_curves[part_num]
                                survival_curve = {
                                    'kmf': part_level_curve['kmf'],
                                    'survival_data': part_level_curve['survival_data'],
                                    'target_serial': serial,
                                    'similar_serials': [],
                                    'total_data_points': part_level_curve['total_data_points'],
                                    'target_data_points': len(serial_data),
                                    'similar_data_points': 0,
                                    'use_part_level': True
                                }
                                self.survival_curves[serial] = survival_curve
                                part_successful += 1
                                survival_data_count = survival_curve['total_data_points']
                                part_survival_stats['total_survival_data'] += survival_data_count
                                part_survival_stats['serial_details'].append({
                                    'serial': serial,
                                    'status': 'part_level_km_insufficient_data',
                                    'survival_data_count': survival_data_count,
                                    'similar_serials': 0
                                })
                                self.log_debug(f"✅ 自身维修次数不足，使用件号级KM曲线，数据量: {survival_data_count}")
                            else:
                                part_failed += 1
                                part_survival_stats['serial_details'].append({
                                    'serial': serial,
                                    'status': 'no_similar_serials',
                                    'survival_data_count': 0,
                                    'similar_serials': 0
                                })
                                self.log_debug(f"❌ 序号 {serial} 无相似序号且维修次数不足，件号级KM曲线也不可用")
                            continue
                    
                    # 建立加权KM生存曲线（关闭可视化，保留数据）
                    survival_curve = self.build_weighted_km_curve_no_plot(serial, serial_data, similar_serials, part_df)
                    
                    if survival_curve is not None:
                        survival_data_count = survival_curve['total_data_points']
                        
                        # 检查生存曲线数据量是否达到阈值
                        if survival_data_count < self.survival_curve_data_threshold:
                            self.log_debug(f"生存曲线数据量({survival_data_count}) < 阈值({self.survival_curve_data_threshold})，尝试使用件号级KM曲线")
                            
                            # 尝试使用件号级KM生存曲线
                            if part_num in self.part_level_km_curves:
                                part_level_curve = self.part_level_km_curves[part_num]
                                part_level_data_count = part_level_curve['total_data_points']
                                
                                if part_level_data_count > survival_data_count:
                                    # 件号级数据量更多，使用件号级生存曲线
                                    survival_curve = {
                                        'kmf': part_level_curve['kmf'],
                                        'survival_data': part_level_curve['survival_data'],
                                        'target_serial': serial,
                                        'similar_serials': [],
                                        'total_data_points': part_level_curve['total_data_points'],
                                        'target_data_points': len(serial_data),
                                        'similar_data_points': 0,
                                        'use_part_level': True
                                    }
                                    survival_data_count = part_level_data_count
                                    self.log_debug(f"✅ 使用件号级KM曲线，数据量提升: {survival_data_count}")
                                    status = 'part_level_km_success'
                                else:
                                    self.log_debug(f"件号级数据量({part_level_data_count})不比序号级数据量大，保持序号级曲线")
                                    status = 'serial_level_km_low_data'
                            else:
                                self.log_debug(f"件号级KM曲线不可用，保持序号级曲线")
                                status = 'serial_level_km_low_data'
                        else:
                            status = 'success'
                        
                        self.survival_curves[serial] = survival_curve
                        part_successful += 1
                        part_survival_stats['total_survival_data'] += survival_data_count
                        part_survival_stats['similar_serials_count'] += len(similar_serials)
                        
                        part_survival_stats['serial_details'].append({
                            'serial': serial,
                            'status': status,
                            'survival_data_count': survival_data_count,
                            'similar_serials': len(similar_serials)
                        })
                        
                        self.log_debug(f"✅ 成功建立生存曲线，相似序号数: {len(similar_serials)}, 数据量: {survival_data_count}")
                    else:
                        # 序号级生存曲线建立失败，尝试使用件号级KM曲线
                        if part_num in self.part_level_km_curves:
                            self.log_debug(f"序号级生存曲线失败，尝试使用件号级KM曲线")
                            part_level_curve = self.part_level_km_curves[part_num]
                            survival_curve = {
                                'kmf': part_level_curve['kmf'],
                                'survival_data': part_level_curve['survival_data'],
                                'target_serial': serial,
                                'similar_serials': [],
                                'total_data_points': part_level_curve['total_data_points'],
                                'target_data_points': len(serial_data),
                                'similar_data_points': 0,
                                'use_part_level': True
                            }
                            self.survival_curves[serial] = survival_curve
                            part_successful += 1
                            survival_data_count = survival_curve['total_data_points']
                            part_survival_stats['total_survival_data'] += survival_data_count
                            part_survival_stats['serial_details'].append({
                                'serial': serial,
                                'status': 'fallback_part_level_km',
                                'survival_data_count': survival_data_count,
                                'similar_serials': 0
                            })
                            self.log_debug(f"✅ 使用件号级KM曲线作为fallback，数据量: {survival_data_count}")
                        else:
                            part_failed += 1
                            part_survival_stats['serial_details'].append({
                                'serial': serial,
                                'status': 'curve_failed',
                                'survival_data_count': 0,
                                'similar_serials': len(similar_serials)
                            })
                            self.log_debug(f"❌ 建立生存曲线失败，件号级KM曲线也不可用")
                    
                except Exception as e:
                    self.log_debug(f"处理序号 {serial} 时出错: {e}", "ERROR")
                    part_failed += 1
                    part_survival_stats['serial_details'].append({
                        'serial': serial,
                        'status': 'error',
                        'survival_data_count': 0,
                        'similar_serials': 0,
                        'error': str(e)
                    })
                    continue
            
            # 更新件号统计
            part_survival_stats['successful_curves'] = part_successful
            part_survival_stats['failed_curves'] = part_failed
            self.part_processing_results[part_num] = part_survival_stats
            
            total_successful_curves += part_successful
            total_failed_curves += part_failed
            
            self.log_debug(f"件号 {part_num} 完成: 成功{part_successful}个，失败{part_failed}个")
            self.log_debug(f"总生存数据量: {part_survival_stats['total_survival_data']}, 相似序号总数: {part_survival_stats['similar_serials_count']}")
            self.log_debug(f"总历史维修次数: {part_survival_stats['total_repairs']}, 件号内序号数: {part_survival_stats['unique_serials_in_part']}")
            self.log_debug(f"平均维修次数/序号: {part_survival_stats['avg_repairs_per_serial']:.1f}")
        
        self.log_debug(f"✅ 按件号生存曲线建立完成: 总成功{total_successful_curves}个，总失败{total_failed_curves}个")
        self.log_debug(f"总成功率: {total_successful_curves/(total_successful_curves+total_failed_curves)*100:.1f}%")

    def adjust_tsr_by_percentile(self, tsr_predictions, processed_df):
        """根据分位数调整TSR预测值"""
        try:
            self.log_debug(f"开始TSR分位数调整: {self.tsr_percentile:.1%}")
            
            adjusted_predictions = tsr_predictions.copy()
            adjustment_stats = []
            
            # 按件号分组调整
            for part_num in tsr_predictions['件号'].unique():
                part_mask = tsr_predictions['件号'] == part_num
                part_tsr_data = tsr_predictions[part_mask]['预测TSR'].dropna()
                
                if len(part_tsr_data) == 0:
                    continue
                
                # 获取该件号的历史TSR数据用于分位数计算
                part_historical = processed_df[processed_df['件号'] == part_num]['TSR'].dropna()
                
                if len(part_historical) > 5:  # 至少需要5个历史数据点
                    # 使用历史TSR数据计算分位数调整因子
                    historical_median = part_historical.median()
                    historical_target_percentile = part_historical.quantile(self.tsr_percentile)
                    
                    if historical_median > 0:
                        adjustment_factor = historical_target_percentile / historical_median
                        
                        # 应用调整
                        original_values = part_tsr_data.values
                        adjusted_values = original_values * adjustment_factor
                        
                        # 更新预测值
                        adjusted_predictions.loc[part_mask, '预测TSR'] = adjusted_predictions.loc[part_mask, '预测TSR'] * adjustment_factor
                        
                        adjustment_stats.append({
                            '件号': part_num,
                            '序号数量': len(part_tsr_data),
                            '历史数据点': len(part_historical),
                            '调整因子': adjustment_factor,
                            '原始平均TSR': original_values.mean(),
                            '调整后平均TSR': adjusted_values.mean()
                        })
                        
                        self.log_debug(f"件号 {part_num}: 调整因子={adjustment_factor:.3f}, 序号数={len(part_tsr_data)}")
                    else:
                        self.log_debug(f"件号 {part_num}: 历史中位数为0，跳过调整")
                else:
                    self.log_debug(f"件号 {part_num}: 历史数据不足({len(part_historical)}点)，跳过调整")
            
            # 保存调整统计
            if adjustment_stats:
                adjustment_df = pd.DataFrame(adjustment_stats)
                adjustment_file = os.path.join(self.debug_dir, "tsr_percentile_adjustment.xlsx")
                adjustment_df.to_excel(adjustment_file, index=False)
                self.log_debug(f"TSR分位数调整统计已保存: {adjustment_file}")
                
                total_adjusted = adjustment_df['序号数量'].sum()
                avg_adjustment_factor = adjustment_df['调整因子'].mean()
                self.log_debug(f"✅ TSR分位数调整完成: 调整{total_adjusted}个序号，平均调整因子={avg_adjustment_factor:.3f}")
            
            return adjusted_predictions
            
        except Exception as e:
            self.log_debug(f"TSR分位数调整失败: {e}", "ERROR")
            return tsr_predictions

    def get_confidence_from_survival_curve(self, kmf, predicted_tsr):
        """从生存曲线中获取置信度"""
        try:
            if kmf is None or predicted_tsr is None or pd.isna(predicted_tsr):
                return None
            
            # 获取生存概率
            survival_prob = kmf.survival_function_at_times(predicted_tsr)
            
            if len(survival_prob) == 0:
                # 如果预测时间超出范围，使用最大时间的生存概率
                max_time = kmf.timeline.max()
                survival_prob = kmf.survival_function_at_times(max_time)
            
            if len(survival_prob) == 0:
                return None
            
            # 置信度 = 1 - 生存概率（即失效概率）
            confidence = 1 - survival_prob.iloc[0]
            
            return max(0.0, min(1.0, confidence))  # 限制在[0,1]范围内
            
        except Exception as e:
            self.log_debug(f"置信度计算失败: {e}", "ERROR")
            return None

    def step3_calculate_confidence(self, processed_df):
        """步骤3：计算置信度
        【KM生存曲线样本量建议】见文件头注释。
        若生存曲线数据量<10，则在结果表格和日志中给出警告。
        """
        self.log_debug("=== 步骤3：计算置信度 ===", "STEP")
        
        if self.tsr_predictions is None:
            self.log_debug("❌ TSR预测结果为空", "ERROR")
            return None
        
        if not self.survival_curves:
            self.log_debug("❌ 生存曲线为空", "ERROR")
            return None
        
        # 应用TSR分位数调整
        if self.tsr_percentile != 0.5:
            self.log_debug(f"应用TSR分位数调整: {self.tsr_percentile:.1%}")
            self.tsr_predictions = self.adjust_tsr_by_percentile(self.tsr_predictions, processed_df)
        
        # 准备最终结果
        final_results = []
        
        for _, row in self.tsr_predictions.iterrows():
            serial = row['序号']
            part_num = row['件号']
            predicted_tsr = row['预测TSR']
            
            # 检查是否有生存曲线
            if serial in self.survival_curves:
                survival_curve = self.survival_curves[serial]
                kmf = survival_curve['kmf']
                survival_data_count = survival_curve['total_data_points']
                # 置信度
                confidence = self.get_confidence_from_survival_curve(kmf, predicted_tsr)
                # 样本量警告
                if survival_data_count < 10:
                    km_sample_warning = "样本量极小，仅供参考"
                    self.log_debug(f"⚠️ 序号 {serial} 生存曲线数据量仅{survival_data_count}，结果仅供参考")
                else:
                    km_sample_warning = ""
                if confidence is not None:
                    self.log_debug(f"序号 {serial}: TSR={predicted_tsr:.1f}h -> 置信度={confidence:.3f}，生存曲线数据量: {survival_data_count} {km_sample_warning}")
                else:
                    self.log_debug(f"序号 {serial}: 置信度计算失败，生存曲线数据量: {survival_data_count} {km_sample_warning}")
                    confidence = None
            else:
                survival_data_count = 0
                km_sample_warning = "无生存曲线"
                self.log_debug(f"序号 {serial}: 无生存曲线")
                confidence = None
            
            # 获取该序号的总维修次数
            serial_repair_count = len(processed_df[processed_df['序号'] == serial]) if processed_df is not None else 0
            
            # 添加到结果中
            result_row = {
                '件号': part_num,
                '序号': serial,
                '预测TSR': predicted_tsr,
                '置信度': confidence,
                '模型版本': MODEL_VERSION,
                '总维修次数': serial_repair_count,
                'KM样本量警告': km_sample_warning
            }
            
            # 添加生存曲线统计信息
            if serial in self.survival_curves:
                result_row.update({
                    '生存曲线建立状态': '成功',
                    '生存曲线数据量': survival_curve['total_data_points'],
                    '相似序号数目': len(survival_curve['similar_serials'])
                })
            else:
                result_row.update({
                    '生存曲线建立状态': '失败',
                    '生存曲线数据量': 0,
                    '相似序号数目': 0
                })
            
            final_results.append(result_row)
        
        self.final_results = pd.DataFrame(final_results)
        
        # 统计信息
        total_serials = len(self.final_results)
        successful_confidence = len(self.final_results[self.final_results['置信度'].notna()])
        
        self.log_debug(f"✅ 置信度计算完成")
        self.log_debug(f"总序号数: {total_serials}")
        self.log_debug(f"成功计算置信度: {successful_confidence}")
        self.log_debug(f"成功率: {successful_confidence/total_serials*100:.1f}%")
        
        if successful_confidence > 0:
            avg_confidence = self.final_results['置信度'].dropna().mean()
            self.log_debug(f"平均置信度: {avg_confidence:.3f}")
        
        return self.final_results

    def step4_save_final_results(self):
        """步骤4：保存最终结果，并输出每个件序号的KM生存曲线数据"""
        self.log_debug("=== 步骤4：保存最终结果 ===", "STEP")
        
        # 检查输出目录和结果是否存在
        if self.output_dir is None:
            self.log_debug("❌ 输出目录未创建", "ERROR")
            return None
            
        if self.final_results is None or len(self.final_results) == 0:
            self.log_debug("❌ 最终结果为空", "ERROR")
            return None
        
        # 保存到Excel文件
        output_file = os.path.join(self.output_dir, f"按件号TSR预测与置信度分析结果_{MODEL_VERSION}.xlsx")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 主要结果
            self.final_results.to_excel(writer, sheet_name='TSR预测与置信度', index=False)
            
            # 按件号统计
            part_summary = self.generate_part_summary()
            part_summary.to_excel(writer, sheet_name='按件号统计', index=False)
            
            # 生存曲线统计
            survival_stats = self.generate_survival_statistics()
            survival_stats.to_excel(writer, sheet_name='生存曲线统计', index=False)
            
            # 原始TSR预测结果
            if self.tsr_predictions is not None:
                self.tsr_predictions.to_excel(writer, sheet_name='原始TSR预测', index=False)
            
            # 新增：整合所有件序号的KM生存曲线总表
            try:
                # 建立 序号->件号 映射（字符串对齐）
                serial_to_part = {}
                if self.tsr_predictions is not None and '序号' in self.tsr_predictions.columns and '件号' in self.tsr_predictions.columns:
                    tmp = self.tsr_predictions[['序号', '件号']].copy()
                    tmp['序号'] = tmp['序号'].astype(str)
                    serial_to_part = dict(zip(tmp['序号'], tmp['件号']))
                
                all_rows = []
                for serial, curve in self.survival_curves.items():
                    kmf = curve['kmf']
                    # 时间轴与生存概率
                    timeline = kmf.survival_function_.index.values
                    survival_prob = kmf.survival_function_['KM_estimate'].values
                    # 置信区间（若存在）
                    ci_lower = None
                    ci_upper = None
                    if hasattr(kmf, 'confidence_interval_') and kmf.confidence_interval_ is not None:
                        ci_df = kmf.confidence_interval_
                        if 'KM_estimate_lower_0.95' in ci_df.columns and 'KM_estimate_upper_0.95' in ci_df.columns:
                            ci_lower = ci_df['KM_estimate_lower_0.95'].values
                            ci_upper = ci_df['KM_estimate_upper_0.95'].values
                    part_num = serial_to_part.get(str(serial))
                    for i in range(len(timeline)):
                        row = {
                            '件号': part_num,
                            '序号': serial,
                            'TSR': float(timeline[i]) if timeline is not None else None,
                            '生存置信度': float(survival_prob[i]) if survival_prob is not None else None,
                        }
                        if ci_lower is not None and ci_upper is not None and i < len(ci_lower) and i < len(ci_upper):
                            row['CI下界0.95'] = float(ci_lower[i])
                            row['CI上界0.95'] = float(ci_upper[i])
                        else:
                            row['CI下界0.95'] = None
                            row['CI上界0.95'] = None
                        all_rows.append(row)
                km_all_df = pd.DataFrame(all_rows, columns=['件号','序号','TSR','生存置信度','CI下界0.95','CI上界0.95'])
                km_all_df.to_excel(writer, sheet_name='KM生存曲线总表', index=False)
                self.log_debug(f"✅ 已生成'KM生存曲线总表'工作表，包含 {len(km_all_df)} 行")
            except Exception as e:
                self.log_debug(f"⚠️ 生成'KM生存曲线总表'失败: {e}", "ERROR")
            
            # 新增：同件号且相似度>=0.7 的相似序号明细
            try:
                if hasattr(self, 'all_similarity_results') and self.all_similarity_results:
                    sim_df = pd.DataFrame(self.all_similarity_results)
                    # 规范列存在性
                    expected_cols = {'目标件号','目标序号','备选件号','备选序号','相似度'}
                    if expected_cols.issubset(set(sim_df.columns)):
                        # 过滤同件号且相似度>=0.7（沿用保存阈值）
                        threshold = max(0.0, SIMILARITY_THRESHOLD_FOR_SAVING)
                        filtered = sim_df[(sim_df['目标件号'] == sim_df['备选件号']) & (sim_df['相似度'] >= threshold)].copy()
                        # 输出精简列并重命名
                        if not filtered.empty:
                            out_df = filtered[['目标件号','目标序号','备选序号','相似度']].copy()
                            out_df.rename(columns={'备选序号':'相似序号'}, inplace=True)
                        else:
                            out_df = pd.DataFrame(columns=['目标件号','目标序号','相似序号','相似度'])
                        out_df.sort_values(by=['目标件号','目标序号','相似度'], ascending=[True, True, False], inplace=True)
                        out_df.to_excel(writer, sheet_name='同件号高相似序号(>=0.7)', index=False)
                        self.log_debug(f"✅ 已生成'同件号高相似序号(>=0.7)'工作表，包含 {len(out_df)} 行")
                    else:
                        # 列不完整则跳过
                        self.log_debug("相似度结果列不完整，跳过'同件号高相似序号(>=0.7)'输出")
                else:
                    # 无相似度记录时输出空表，保证结构一致
                    empty_df = pd.DataFrame(columns=['目标件号','目标序号','相似序号','相似度'])
                    empty_df.to_excel(writer, sheet_name='同件号高相似序号(>=0.7)', index=False)
                    self.log_debug("相似度记录为空，已生成空的'同件号高相似序号(>=0.7)'工作表")
            except Exception as e:
                self.log_debug(f"⚠️ 生成'同件号高相似序号(>=0.7)'失败: {e}", "ERROR")
        
        self.log_debug(f"✅ 最终结果已保存至: {output_file}")
        
        # 新增：保存相似度分析过程文件
        self._save_similarity_details()

        # 保存每个件序号的KM生存曲线数据
        for serial, curve in self.survival_curves.items():
            kmf = curve['kmf']
            timeline = kmf.survival_function_.index.values
            survival_prob = kmf.survival_function_['KM_estimate'].values
            ci_lower = kmf.confidence_interval_['KM_estimate_lower_0.95'].values if 'KM_estimate_lower_0.95' in kmf.confidence_interval_.columns else None
            ci_upper = kmf.confidence_interval_['KM_estimate_upper_0.95'].values if 'KM_estimate_upper_0.95' in kmf.confidence_interval_.columns else None
            df = pd.DataFrame({
                'timeline': timeline,
                'survival_probability': survival_prob
            })
            if ci_lower is not None and ci_upper is not None:
                df['ci_lower_0.95'] = ci_lower
                df['ci_upper_0.95'] = ci_upper
            km_curve_file = os.path.join(self.survival_data_dir, f"{serial}_km_curve.xlsx")
            df.to_excel(km_curve_file, index=False)
            self.log_debug(f"KM生存曲线数据已保存: {km_curve_file}")
        
        # 保存调试日志
        log_file = os.path.join(self.debug_dir, "debug_log.txt")
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.debug_log))
        
        self.log_debug(f"✅ 调试日志已保存至: {log_file}")
        
        return output_file

    def _save_similarity_details(self):
        """保存高于阈值的相似度分析结果"""
        if not SAVE_SIMILARITY_RESULTS or not self.all_similarity_results:
            return

        try:
            self.log_debug("正在保存相似度分析过程记录...")
            similarity_df = pd.DataFrame(self.all_similarity_results)
            similarity_df.sort_values(by=['目标件号', '目标序号', '相似度'], ascending=[True, True, False], inplace=True)
            
            output_path = os.path.join(self.statistics_dir, "相似度分析过程记录.xlsx")
            similarity_df.to_excel(output_path, index=False)
            self.log_debug(f"✅ 相似度分析过程记录已保存至: {output_path}")

        except Exception as e:
            self.log_debug(f"❌ 保存相似度分析过程记录失败: {e}", "ERROR")

    def generate_part_summary(self):
        """生成按件号统计摘要"""
        if not self.part_processing_results:
            return pd.DataFrame()
        
        summary_data = []
        
        for part_num, stats in self.part_processing_results.items():
            summary_data.append({
                '件号': part_num,
                '总序号数': stats['total_serials'],
                '成功建立生存曲线数': stats['successful_curves'],
                '失败生存曲线数': stats['failed_curves'],
                '成功率': f"{stats['successful_curves']/stats['total_serials']*100:.1f}%" if stats['total_serials'] > 0 else "0%",
                '总生存数据量': stats['total_survival_data'],
                '相似序号总数': stats['similar_serials_count'],
                '平均相似序号数': f"{stats['similar_serials_count']/stats['successful_curves']:.1f}" if stats['successful_curves'] > 0 else "0",
                '总历史维修次数': stats['total_repairs'],
                '件号内序号数': stats['unique_serials_in_part'],
                '平均维修次数/序号': f"{stats['avg_repairs_per_serial']:.1f}"
            })
        
        return pd.DataFrame(summary_data)

    def generate_survival_statistics(self):
        """生成生存曲线统计"""
        if not self.final_results is not None:
            return pd.DataFrame()
        
        # 总体统计
        total_serials = len(self.final_results)
        successful_confidence = len(self.final_results[self.final_results['置信度'].notna()])
        successful_survival_curves = len(self.final_results[self.final_results['生存曲线建立状态'] == '成功'])
        failed_survival_curves = len(self.final_results[self.final_results['生存曲线建立状态'] == '失败'])
        total_survival_data = self.final_results['生存曲线数据量'].sum()
        total_similar_serials = self.final_results['相似序号数目'].sum()
        
        stats_data = [
            {
                '统计项': '总序号数',
                '数值': total_serials,
                '说明': '需要分析的序号总数'
            },
            {
                '统计项': '成功建立生存曲线数',
                '数值': successful_survival_curves,
                '说明': '成功建立生存曲线的序号数量'
            },
            {
                '统计项': '失败建立生存曲线数',
                '数值': failed_survival_curves,
                '说明': '失败建立生存曲线的序号数量'
            },
            {
                '统计项': '生存曲线建立成功率',
                '数值': f"{(successful_survival_curves/total_serials*100):.1f}%",
                '说明': '成功建立生存曲线的比例'
            },
            {
                '统计项': '成功计算置信度序号数',
                '数值': successful_confidence,
                '说明': '成功计算出置信度的序号数量'
            },
            {
                '统计项': '置信度计算成功率',
                '数值': f"{(successful_confidence/total_serials*100):.1f}%",
                '说明': '成功计算置信度的比例'
            },
            {
                '统计项': '总生存曲线数据量',
                '数值': total_survival_data,
                '说明': '所有生存曲线的数据点总数'
            },
            {
                '统计项': '总相似序号数',
                '数值': total_similar_serials,
                '说明': '所有相似序号的总数'
            },
            {
                '统计项': '平均生存曲线数据量',
                '数值': f"{total_survival_data/successful_survival_curves:.1f}" if successful_survival_curves > 0 else "0",
                '说明': '每个成功建立生存曲线的序号的平均数据量'
            },
            {
                '统计项': '平均相似序号数',
                '数值': f"{total_similar_serials/successful_survival_curves:.1f}" if successful_survival_curves > 0 else "0",
                '说明': '每个成功建立生存曲线的序号的平均相似序号数'
            }
        ]
        
        # 置信度统计
        valid_confidence = self.final_results['置信度'].dropna()
        if len(valid_confidence) > 0:
            stats_data.extend([
                {
                    '统计项': '平均置信度',
                    '数值': f"{valid_confidence.mean():.3f}",
                    '说明': '所有有效置信度的平均值'
                },
                {
                    '统计项': '置信度中位数',
                    '数值': f"{valid_confidence.median():.3f}",
                    '说明': '置信度的中位数'
                },
                {
                    '统计项': '高置信度序号数(>0.7)',
                    '数值': len(valid_confidence[valid_confidence > 0.7]),
                    '说明': '置信度大于0.7的序号数量'
                }
            ])
        
        return pd.DataFrame(stats_data)

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        self.log_debug("=== 按件号TSR预测与置信度分析系统 ===", "START")
        self.log_debug(f"模型版本: {MODEL_VERSION}")
        self.log_debug(f"输入文件: {self.repair_history_file}")
        
        # 创建输出目录
        self.create_output_directory()
        
        # 读取数据
        try:
            self.log_debug(f"正在读取数据文件: {self.repair_history_file}")
            df = pd.read_excel(self.repair_history_file)
            self.log_debug(f"✅ 成功读取数据: {len(df)} 条记录")
        except Exception as e:
            self.log_debug(f"❌ 读取数据失败: {e}", "ERROR")
            return None
        
        try:
            if self.enable_step1_prediction:
                # 步骤1：TSR预测（建模+预测）
                processed_df, part_results = self.step1_predict_tsr(df)
            else:
                # 跳过建模，直接从外部文件读取预测TSR
                self.log_debug("=== 跳过步骤1：从外部文件读取预测TSR ===", "STEP")
                # 仅进行数据处理以供后续KM使用
                self.log_debug("开始处理数据（无建模，仅供KM曲线使用）...")
                # 数据清洗（根据规则剔除异常记录）
                df_clean = self.apply_input_cleaning_rules(df)
                self.log_debug(f"清洗后输入数据: {len(df_clean)} / 原始 {len(df)} 条")
                processed_df = self.tsr_analyzer.process_data(df_clean)
                self.log_debug(f"数据处理完成，共 {len(processed_df)} 条记录")
                
                # 读取外部预测文件
                pred_path = self.external_prediction_file
                self.log_debug(f"读取外部TSR预测文件: {pred_path}")
                try:
                    try:
                        tsr_pred_df = pd.read_excel(pred_path, sheet_name='预测结果')
                    except Exception:
                        tsr_pred_df = pd.read_excel(pred_path)
                    # 规范列名
                    tsr_pred_df.columns = tsr_pred_df.columns.map(str).str.strip()
                    required_cols = {'件号', '序号', '预测TSR'}
                    missing = required_cols - set(tsr_pred_df.columns)
                    if missing:
                        raise ValueError(f"外部预测文件缺少必要列: {missing}")
                    self.tsr_predictions = tsr_pred_df[['件号', '序号', '预测TSR']].copy()
                    # 保存调试副本
                    debug_file = os.path.join(self.debug_dir, "tsr_predictions_external.xlsx")
                    self.tsr_predictions.to_excel(debug_file, index=False)
                    self.log_debug(f"✅ 已加载外部预测TSR: {len(self.tsr_predictions)} 条；调试输出: {debug_file}")
                except Exception as e:
                    self.log_debug(f"❌ 读取外部预测TSR失败: {e}", "ERROR")
                    raise
            
            # 步骤2：按件号建立生存曲线（使用 self.tsr_predictions 中的件号/序号范围）
            self.step2_build_survival_curves_by_part(processed_df)
            
            # 步骤3：计算置信度
            final_results = self.step3_calculate_confidence(processed_df)
            
            # 步骤4：保存最终结果
            output_file = self.step4_save_final_results()
            
            self.log_debug("\n" + "=" * 80)
            self.log_debug("✅ 分析完成！", "SUCCESS")
            self.log_debug("=" * 80)
            self.log_debug(f"📊 总序号数: {len(final_results)}")
            self.log_debug(f"📊 成功计算置信度: {len(final_results[final_results['置信度'].notna()])}")
            self.log_debug(f"📊 输出文件: {output_file}")
            self.log_debug(f"📊 分析目录: {self.output_dir}")
            
            return output_file
            
        except Exception as e:
            self.log_debug(f"❌ 分析过程中出错: {e}", "ERROR")
            self.log_debug(f"错误详情: {traceback.format_exc()}", "ERROR")
            return None

    def apply_input_cleaning_rules(self, df: pd.DataFrame) -> pd.DataFrame:
        """根据配置规则清洗原始输入数据（仅作用于输入文件级别）。
        规则：
        - 若存在列 '组异常类型'，剔除值属于 exclude_group_abnormal_types 的记录
        - 若存在列 '单条记录异常类型'，剔除值属于 exclude_single_record_abnormal_types 的记录
        """
        try:
            rules = self.data_cleaning_rules or DEFAULT_CLEANING_RULES
            df_clean = df.copy()
            # 统一列名去空格
            df_clean.columns = df_clean.columns.map(str).str.strip()

            # 剔除组异常类型
            group_col = '组异常类型'
            if group_col in df_clean.columns:
                to_exclude = set([str(x).strip() for x in rules.get('exclude_group_abnormal_types', []) if str(x).strip()])
                if to_exclude:
                    mask = df_clean[group_col].astype(str).isin(to_exclude)
                    removed = int(mask.sum())
                    if removed > 0:
                        self.log_debug(f"按组异常类型剔除 {removed} 条: {sorted(list(to_exclude))}")
                    df_clean = df_clean[~mask]

            # 剔除单条记录异常类型
            single_col = '单条记录异常类型'
            if single_col in df_clean.columns:
                to_exclude = set([str(x).strip() for x in rules.get('exclude_single_record_abnormal_types', []) if str(x).strip()])
                if to_exclude:
                    mask = df_clean[single_col].astype(str).isin(to_exclude)
                    removed = int(mask.sum())
                    if removed > 0:
                        self.log_debug(f"按单条记录异常类型剔除 {removed} 条: {sorted(list(to_exclude))}")
                    df_clean = df_clean[~mask]

            df_clean.reset_index(drop=True, inplace=True)
            return df_clean
        except Exception as e:
            self.log_debug(f"数据清洗规则应用失败: {e}", "ERROR")
            return df

def main():
    """主函数"""
    # 可在此处调整超参数
    # tsr_percentile: TSR预测分位数 (0.5=50%分位数, 0.6=60%分位数)
    # survival_curve_data_threshold: 生存曲线数据量阈值，小于此值使用件号级KM曲线
    analyzer = PartBasedTSRConfidenceAnalyzer(
        tsr_percentile=0.5,  # 可调整为0.6等
        survival_curve_data_threshold=10,
        similarity_threshold=0.7,
        top_similar_count=10,
        data_cleaning_rules=DEFAULT_CLEANING_RULES
    )
    result = analyzer.run_complete_analysis()
    
    if result:
        print(f"\n🎉 按件号TSR预测与置信度分析成功完成！")
        print(f"📋 最终结果文件: {result}")
    else:
        print("\n❌ 分析失败，请检查错误信息")

if __name__ == '__main__':
    main() 